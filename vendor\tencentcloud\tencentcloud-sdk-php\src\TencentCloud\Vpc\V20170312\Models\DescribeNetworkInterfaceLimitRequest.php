<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Vpc\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeNetworkInterfaceLimit请求参数结构体
 *
 * @method string getInstanceId() 获取要查询的CVM实例ID或弹性网卡ID
 * @method void setInstanceId(string $InstanceId) 设置要查询的CVM实例ID或弹性网卡ID
 */
class DescribeNetworkInterfaceLimitRequest extends AbstractModel
{
    /**
     * @var string 要查询的CVM实例ID或弹性网卡ID
     */
    public $InstanceId;

    /**
     * @param string $InstanceId 要查询的CVM实例ID或弹性网卡ID
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InstanceId",$param) and $param["InstanceId"] !== null) {
            $this->InstanceId = $param["InstanceId"];
        }
    }
}
