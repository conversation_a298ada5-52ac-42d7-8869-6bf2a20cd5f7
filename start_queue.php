<?php
/**
 * 启动队列监听脚本
 * 用于启动订单取消队列的监听进程
 */

echo "启动订单取消队列监听...\n";
echo "队列名称: orderCancel\n";
echo "Redis连接: r-bp180wgq6voudt9d75.redis.rds.aliyuncs.com\n";
echo "按 Ctrl+C 停止监听\n";
echo "==========================================\n";

// 执行队列监听命令
$command = 'php think queue:listen --queue=orderCancel --delay=0 --sleep=3 --tries=3 --memory=128 --timeout=300';

echo "执行命令: {$command}\n";
echo "==========================================\n";

// 使用 passthru 执行命令，这样可以实时看到输出
passthru($command);
