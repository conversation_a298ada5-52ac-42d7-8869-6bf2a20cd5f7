# 商品图片预览功能优化说明

## 问题描述
原来的商品图片点击放大功能存在以下问题：
1. 点击图片时会同时显示两个弹窗（一个大的，一个小的）
2. 多个图片预览方法冲突（function.js 和 like.js 中都有 showImg 方法）
3. 缺乏统一的管理机制

## 解决方案

### 1. 新增统一的图片预览方法
在 `public/static/admin/js/function.js` 中新增了 `goodsImagePreview` 方法：

```javascript
like.goodsImagePreview(url, maxSize)
```

**参数说明：**
- `url`: 图片URL地址
- `maxSize`: 最大显示尺寸，默认500px

**功能特点：**
- 防重复调用锁定机制
- 自动计算合适的显示尺寸
- 保持图片宽高比
- 支持点击图片关闭
- 支持ESC键关闭
- 支持点击遮罩关闭

### 2. 优化图片点击事件
更新了 `.preview-image` 类的点击事件处理：

```javascript
$(document).on('click', '.preview-image', function(e) {
    e.preventDefault();
    e.stopPropagation();
    
    var imgSrc = $(this).data('src') || $(this).attr('src');
    if (imgSrc) {
        like.goodsImagePreview(imgSrc, 600);
    }
});
```

**改进点：**
- 添加了100ms防抖机制，防止快速重复点击
- 统一使用新的预览方法
- 更好的事件处理

### 3. 防重复弹窗机制
- 调用前先关闭所有现有弹窗：`layer.closeAll()`
- 添加调用锁定：`this._previewLock`
- 防抖处理：`setTimeout` 延迟执行

## 使用方法

### 在商品添加/编辑页面
图片会自动添加 `preview-image` 类，点击即可放大预览：

```html
<img src="图片URL" class="preview-image" data-src="高清图片URL" title="点击放大查看" />
```

### 手动调用预览
```javascript
// 基本调用
like.goodsImagePreview('图片URL');

// 指定最大尺寸
like.goodsImagePreview('图片URL', 800);
```

## 测试页面
创建了 `test_image_preview.html` 测试页面，可以验证：
1. 新方法是否正常工作
2. 是否还有重复弹窗问题
3. 不同尺寸图片的显示效果

## 兼容性说明
- 保留了原有的 `like.showImg` 方法，确保其他功能不受影响
- 新方法仅用于商品图片预览，不影响其他模块
- 支持所有现代浏览器

## 注意事项
1. 确保图片URL有效，无效URL会显示错误提示
2. 建议为图片设置合适的 `data-src` 属性指向高清版本
3. 如需自定义样式，可以修改弹窗的 content 部分

## 文件修改清单
- `public/static/admin/js/function.js` - 新增 goodsImagePreview 方法，优化点击事件
- `test_image_preview.html` - 测试页面（可选）
- `商品图片预览功能说明.md` - 本说明文档

## 后续优化建议
1. 可以考虑添加图片加载进度提示
2. 支持图片缩放和拖拽功能
3. 添加图片切换功能（上一张/下一张）
4. 支持全屏预览模式
