<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ms\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * so加固信息
 *
 * @method array getSoFileNames() 获取so文件列表
 * @method void setSoFileNames(array $SoFileNames) 设置so文件列表
 */
class SoInfo extends AbstractModel
{
    /**
     * @var array so文件列表
     */
    public $SoFileNames;

    /**
     * @param array $SoFileNames so文件列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SoFileNames",$param) and $param["SoFileNames"] !== null) {
            $this->SoFileNames = $param["SoFileNames"];
        }
    }
}
