<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Vpc\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeIpGeolocationDatabaseUrl请求参数结构体
 *
 * @method string getType() 获取IP地理位置库协议类型，目前仅支持"ipv4"。
 * @method void setType(string $Type) 设置IP地理位置库协议类型，目前仅支持"ipv4"。
 */
class DescribeIpGeolocationDatabaseUrlRequest extends AbstractModel
{
    /**
     * @var string IP地理位置库协议类型，目前仅支持"ipv4"。
     */
    public $Type;

    /**
     * @param string $Type IP地理位置库协议类型，目前仅支持"ipv4"。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Type",$param) and $param["Type"] !== null) {
            $this->Type = $param["Type"];
        }
    }
}
