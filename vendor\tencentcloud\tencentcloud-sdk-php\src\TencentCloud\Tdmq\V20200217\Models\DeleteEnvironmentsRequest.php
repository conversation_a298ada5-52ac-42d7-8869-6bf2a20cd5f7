<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tdmq\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteEnvironments请求参数结构体
 *
 * @method array getEnvironmentIds() 获取环境（命名空间）数组，每次最多删除20个。
 * @method void setEnvironmentIds(array $EnvironmentIds) 设置环境（命名空间）数组，每次最多删除20个。
 * @method string getClusterId() 获取Pulsar 集群的ID
 * @method void setClusterId(string $ClusterId) 设置Pulsar 集群的ID
 */
class DeleteEnvironmentsRequest extends AbstractModel
{
    /**
     * @var array 环境（命名空间）数组，每次最多删除20个。
     */
    public $EnvironmentIds;

    /**
     * @var string Pulsar 集群的ID
     */
    public $ClusterId;

    /**
     * @param array $EnvironmentIds 环境（命名空间）数组，每次最多删除20个。
     * @param string $ClusterId Pulsar 集群的ID
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("EnvironmentIds",$param) and $param["EnvironmentIds"] !== null) {
            $this->EnvironmentIds = $param["EnvironmentIds"];
        }

        if (array_key_exists("ClusterId",$param) and $param["ClusterId"] !== null) {
            $this->ClusterId = $param["ClusterId"];
        }
    }
}
