<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cwp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeScanMalwareSchedule返回参数结构体
 *
 * @method integer getSchedule() 获取扫描进度
 * @method void setSchedule(integer $Schedule) 设置扫描进度
 * @method integer getRiskFileNumber() 获取风险文件数,当进度满了以后才有该值
 * @method void setRiskFileNumber(integer $RiskFileNumber) 设置风险文件数,当进度满了以后才有该值
 * @method boolean getIsSchedule() 获取是否正在扫描中
 * @method void setIsSchedule(boolean $IsSchedule) 设置是否正在扫描中
 * @method integer getScanStatus() 获取0 从未扫描过、 1 扫描中、 2扫描完成、 3停止中、 4停止完成
 * @method void setScanStatus(integer $ScanStatus) 设置0 从未扫描过、 1 扫描中、 2扫描完成、 3停止中、 4停止完成
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeScanMalwareScheduleResponse extends AbstractModel
{
    /**
     * @var integer 扫描进度
     */
    public $Schedule;

    /**
     * @var integer 风险文件数,当进度满了以后才有该值
     */
    public $RiskFileNumber;

    /**
     * @var boolean 是否正在扫描中
     */
    public $IsSchedule;

    /**
     * @var integer 0 从未扫描过、 1 扫描中、 2扫描完成、 3停止中、 4停止完成
     */
    public $ScanStatus;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param integer $Schedule 扫描进度
     * @param integer $RiskFileNumber 风险文件数,当进度满了以后才有该值
     * @param boolean $IsSchedule 是否正在扫描中
     * @param integer $ScanStatus 0 从未扫描过、 1 扫描中、 2扫描完成、 3停止中、 4停止完成
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Schedule",$param) and $param["Schedule"] !== null) {
            $this->Schedule = $param["Schedule"];
        }

        if (array_key_exists("RiskFileNumber",$param) and $param["RiskFileNumber"] !== null) {
            $this->RiskFileNumber = $param["RiskFileNumber"];
        }

        if (array_key_exists("IsSchedule",$param) and $param["IsSchedule"] !== null) {
            $this->IsSchedule = $param["IsSchedule"];
        }

        if (array_key_exists("ScanStatus",$param) and $param["ScanStatus"] !== null) {
            $this->ScanStatus = $param["ScanStatus"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
