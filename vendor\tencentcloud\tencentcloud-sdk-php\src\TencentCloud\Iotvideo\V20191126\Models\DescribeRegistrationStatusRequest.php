<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Iotvideo\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeRegistrationStatus请求参数结构体
 *
 * @method array getCunionIds() 获取终端用户的唯一ID列表，0<元素数量<=100
 * @method void setCunionIds(array $CunionIds) 设置终端用户的唯一ID列表，0<元素数量<=100
 */
class DescribeRegistrationStatusRequest extends AbstractModel
{
    /**
     * @var array 终端用户的唯一ID列表，0<元素数量<=100
     */
    public $CunionIds;

    /**
     * @param array $CunionIds 终端用户的唯一ID列表，0<元素数量<=100
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("CunionIds",$param) and $param["CunionIds"] !== null) {
            $this->CunionIds = $param["CunionIds"];
        }
    }
}
