<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tem\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 存储卷配置
 *
 * @method string getStorageVolName() 获取存储卷名称
 * @method void setStorageVolName(string $StorageVolName) 设置存储卷名称
 * @method string getStorageVolPath() 获取存储卷路径
 * @method void setStorageVolPath(string $StorageVolPath) 设置存储卷路径
 * @method string getStorageVolIp() 获取存储卷IP
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setStorageVolIp(string $StorageVolIp) 设置存储卷IP
注意：此字段可能返回 null，表示取不到有效值。
 */
class StorageConf extends AbstractModel
{
    /**
     * @var string 存储卷名称
     */
    public $StorageVolName;

    /**
     * @var string 存储卷路径
     */
    public $StorageVolPath;

    /**
     * @var string 存储卷IP
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $StorageVolIp;

    /**
     * @param string $StorageVolName 存储卷名称
     * @param string $StorageVolPath 存储卷路径
     * @param string $StorageVolIp 存储卷IP
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("StorageVolName",$param) and $param["StorageVolName"] !== null) {
            $this->StorageVolName = $param["StorageVolName"];
        }

        if (array_key_exists("StorageVolPath",$param) and $param["StorageVolPath"] !== null) {
            $this->StorageVolPath = $param["StorageVolPath"];
        }

        if (array_key_exists("StorageVolIp",$param) and $param["StorageVolIp"] !== null) {
            $this->StorageVolIp = $param["StorageVolIp"];
        }
    }
}
