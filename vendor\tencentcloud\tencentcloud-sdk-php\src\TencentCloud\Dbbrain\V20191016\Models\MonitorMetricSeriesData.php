<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dbbrain\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 单位时间间隔内的监控指标数据
 *
 * @method array getSeries() 获取监控指标。
 * @method void setSeries(array $Series) 设置监控指标。
 * @method array getTimestamp() 获取监控指标对应的时间戳。
 * @method void setTimestamp(array $Timestamp) 设置监控指标对应的时间戳。
 */
class MonitorMetricSeriesData extends AbstractModel
{
    /**
     * @var array 监控指标。
     */
    public $Series;

    /**
     * @var array 监控指标对应的时间戳。
     */
    public $Timestamp;

    /**
     * @param array $Series 监控指标。
     * @param array $Timestamp 监控指标对应的时间戳。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Series",$param) and $param["Series"] !== null) {
            $this->Series = [];
            foreach ($param["Series"] as $key => $value){
                $obj = new MonitorMetric();
                $obj->deserialize($value);
                array_push($this->Series, $obj);
            }
        }

        if (array_key_exists("Timestamp",$param) and $param["Timestamp"] !== null) {
            $this->Timestamp = $param["Timestamp"];
        }
    }
}
