<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Monitor\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 告警对象所属实例组
 *
 * @method integer getId() 获取实例组 Id
 * @method void setId(integer $Id) 设置实例组 Id
 * @method string getName() 获取实例组名称
 * @method void setName(string $Name) 设置实例组名称
 */
class InstanceGroups extends AbstractModel
{
    /**
     * @var integer 实例组 Id
     */
    public $Id;

    /**
     * @var string 实例组名称
     */
    public $Name;

    /**
     * @param integer $Id 实例组 Id
     * @param string $Name 实例组名称
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Id",$param) and $param["Id"] !== null) {
            $this->Id = $param["Id"];
        }

        if (array_key_exists("Name",$param) and $param["Name"] !== null) {
            $this->Name = $param["Name"];
        }
    }
}
