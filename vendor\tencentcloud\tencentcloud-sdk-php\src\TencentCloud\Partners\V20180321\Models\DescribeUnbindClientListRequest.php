<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Partners\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeUnbindClientList请求参数结构体
 *
 * @method integer getStatus() 获取解绑状态：0:所有,1:审核中,2已解绑
 * @method void setStatus(integer $Status) 设置解绑状态：0:所有,1:审核中,2已解绑
 * @method integer getOffset() 获取偏移量
 * @method void setOffset(integer $Offset) 设置偏移量
 * @method integer getLimit() 获取限制数目
 * @method void setLimit(integer $Limit) 设置限制数目
 * @method string getUnbindUin() 获取解绑账号ID
 * @method void setUnbindUin(string $UnbindUin) 设置解绑账号ID
 * @method string getApplyTimeStart() 获取解绑申请时间范围起始点
 * @method void setApplyTimeStart(string $ApplyTimeStart) 设置解绑申请时间范围起始点
 * @method string getApplyTimeEnd() 获取解绑申请时间范围终止点
 * @method void setApplyTimeEnd(string $ApplyTimeEnd) 设置解绑申请时间范围终止点
 * @method string getOrderDirection() 获取对申请时间的升序降序，值：asc，desc
 * @method void setOrderDirection(string $OrderDirection) 设置对申请时间的升序降序，值：asc，desc
 */
class DescribeUnbindClientListRequest extends AbstractModel
{
    /**
     * @var integer 解绑状态：0:所有,1:审核中,2已解绑
     */
    public $Status;

    /**
     * @var integer 偏移量
     */
    public $Offset;

    /**
     * @var integer 限制数目
     */
    public $Limit;

    /**
     * @var string 解绑账号ID
     */
    public $UnbindUin;

    /**
     * @var string 解绑申请时间范围起始点
     */
    public $ApplyTimeStart;

    /**
     * @var string 解绑申请时间范围终止点
     */
    public $ApplyTimeEnd;

    /**
     * @var string 对申请时间的升序降序，值：asc，desc
     */
    public $OrderDirection;

    /**
     * @param integer $Status 解绑状态：0:所有,1:审核中,2已解绑
     * @param integer $Offset 偏移量
     * @param integer $Limit 限制数目
     * @param string $UnbindUin 解绑账号ID
     * @param string $ApplyTimeStart 解绑申请时间范围起始点
     * @param string $ApplyTimeEnd 解绑申请时间范围终止点
     * @param string $OrderDirection 对申请时间的升序降序，值：asc，desc
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Status",$param) and $param["Status"] !== null) {
            $this->Status = $param["Status"];
        }

        if (array_key_exists("Offset",$param) and $param["Offset"] !== null) {
            $this->Offset = $param["Offset"];
        }

        if (array_key_exists("Limit",$param) and $param["Limit"] !== null) {
            $this->Limit = $param["Limit"];
        }

        if (array_key_exists("UnbindUin",$param) and $param["UnbindUin"] !== null) {
            $this->UnbindUin = $param["UnbindUin"];
        }

        if (array_key_exists("ApplyTimeStart",$param) and $param["ApplyTimeStart"] !== null) {
            $this->ApplyTimeStart = $param["ApplyTimeStart"];
        }

        if (array_key_exists("ApplyTimeEnd",$param) and $param["ApplyTimeEnd"] !== null) {
            $this->ApplyTimeEnd = $param["ApplyTimeEnd"];
        }

        if (array_key_exists("OrderDirection",$param) and $param["OrderDirection"] !== null) {
            $this->OrderDirection = $param["OrderDirection"];
        }
    }
}
