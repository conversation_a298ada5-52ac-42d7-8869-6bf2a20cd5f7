<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Gse\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * UpdateFleetPortSettings请求参数结构体
 *
 * @method string getFleetId() 获取服务器舰队 Id
 * @method void setFleetId(string $FleetId) 设置服务器舰队 Id
 * @method array getInboundPermissionAuthorizations() 获取新增安全组
 * @method void setInboundPermissionAuthorizations(array $InboundPermissionAuthorizations) 设置新增安全组
 * @method array getInboundPermissionRevocations() 获取移除安全组
 * @method void setInboundPermissionRevocations(array $InboundPermissionRevocations) 设置移除安全组
 */
class UpdateFleetPortSettingsRequest extends AbstractModel
{
    /**
     * @var string 服务器舰队 Id
     */
    public $FleetId;

    /**
     * @var array 新增安全组
     */
    public $InboundPermissionAuthorizations;

    /**
     * @var array 移除安全组
     */
    public $InboundPermissionRevocations;

    /**
     * @param string $FleetId 服务器舰队 Id
     * @param array $InboundPermissionAuthorizations 新增安全组
     * @param array $InboundPermissionRevocations 移除安全组
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("FleetId",$param) and $param["FleetId"] !== null) {
            $this->FleetId = $param["FleetId"];
        }

        if (array_key_exists("InboundPermissionAuthorizations",$param) and $param["InboundPermissionAuthorizations"] !== null) {
            $this->InboundPermissionAuthorizations = [];
            foreach ($param["InboundPermissionAuthorizations"] as $key => $value){
                $obj = new InboundPermissionAuthorization();
                $obj->deserialize($value);
                array_push($this->InboundPermissionAuthorizations, $obj);
            }
        }

        if (array_key_exists("InboundPermissionRevocations",$param) and $param["InboundPermissionRevocations"] !== null) {
            $this->InboundPermissionRevocations = [];
            foreach ($param["InboundPermissionRevocations"] as $key => $value){
                $obj = new InboundPermissionRevocations();
                $obj->deserialize($value);
                array_push($this->InboundPermissionRevocations, $obj);
            }
        }
    }
}
