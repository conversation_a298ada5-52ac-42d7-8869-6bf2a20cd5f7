<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Nlp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 实体关系查询返回Subject
 *
 * @method array getPopular() 获取Subject对应popular
 * @method void setPopular(array $Popular) 设置Subject对应popular
 * @method array getId() 获取Subject对应id
 * @method void setId(array $Id) 设置Subject对应id
 * @method array getName() 获取Subject对应name
 * @method void setName(array $Name) 设置Subject对应name
 */
class EntityRelationSubject extends AbstractModel
{
    /**
     * @var array Subject对应popular
     */
    public $Popular;

    /**
     * @var array Subject对应id
     */
    public $Id;

    /**
     * @var array Subject对应name
     */
    public $Name;

    /**
     * @param array $Popular Subject对应popular
     * @param array $Id Subject对应id
     * @param array $Name Subject对应name
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Popular",$param) and $param["Popular"] !== null) {
            $this->Popular = $param["Popular"];
        }

        if (array_key_exists("Id",$param) and $param["Id"] !== null) {
            $this->Id = $param["Id"];
        }

        if (array_key_exists("Name",$param) and $param["Name"] !== null) {
            $this->Name = $param["Name"];
        }
    }
}
