<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ie\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 时间区间。
 *
 * @method integer getStartTime() 获取开始时间点，单位ms
 * @method void setStartTime(integer $StartTime) 设置开始时间点，单位ms
 * @method integer getDuration() 获取时间区间时长，单位ms
 * @method void setDuration(integer $Duration) 设置时间区间时长，单位ms
 */
class SectionTime extends AbstractModel
{
    /**
     * @var integer 开始时间点，单位ms
     */
    public $StartTime;

    /**
     * @var integer 时间区间时长，单位ms
     */
    public $Duration;

    /**
     * @param integer $StartTime 开始时间点，单位ms
     * @param integer $Duration 时间区间时长，单位ms
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("StartTime",$param) and $param["StartTime"] !== null) {
            $this->StartTime = $param["StartTime"];
        }

        if (array_key_exists("Duration",$param) and $param["Duration"] !== null) {
            $this->Duration = $param["Duration"];
        }
    }
}
