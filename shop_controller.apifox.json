{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "商家控制器接口文档", "description": "基于 app/shopapi/controller/Shop.php 自动生成，适用于Apifox导入。", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "商家管理", "id": ********, "auth": {}, "parentId": 0, "description": "商家相关接口", "items": [{"name": "商家基础信息", "id": 46973026, "parentId": ********, "items": [{"name": "获取商家信息及余额", "api": {"method": "get", "path": "/shopapi/shop/getShopInfo", "description": "获取商家可提现余额等基础信息", "parameters": {}, "responses": [{"id": "resp-getShopInfo", "code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"shop_info": {"type": "object", "description": "商家基本信息"}, "balance": {"type": "number", "description": "可提现余额"}}}}}}]}}, {"name": "设置商家信息", "api": {"method": "post", "path": "/shopapi/shop/shopSet", "description": "设置商家基本信息", "requestBody": {"type": "object", "parameters": []}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}}]}}]}, {"name": "提现管理", "id": 46973027, "parentId": ********, "items": [{"name": "获取提现信息", "api": {"method": "get", "path": "/shopapi/shop/getWithdrawInfo", "description": "获取提现相关信息", "responses": [{"code": 200, "name": "成功"}]}}, {"name": "提现操作", "api": {"method": "post", "path": "/shopapi/shop/withdraw", "description": "执行提现操作", "responses": [{"code": 200, "name": "成功"}]}}, {"name": "提现记录", "api": {"method": "get", "path": "/shopapi/shop/withdrawLog", "description": "获取提现记录列表", "parameters": {"query": [{"name": "page_no", "type": "integer", "description": "页码", "required": false}, {"name": "page_size", "type": "integer", "description": "每页数量", "required": false}]}, "responses": [{"code": 200, "name": "成功"}]}}]}, {"name": "银行卡管理", "id": ********, "parentId": ********, "items": [{"name": "添加银行卡", "api": {"method": "post", "path": "/shopapi/shop/addBank", "description": "添加银行卡账户", "responses": [{"code": 200, "name": "成功"}]}}, {"name": "获取银行卡信息", "api": {"method": "get", "path": "/shopapi/shop/getBank", "description": "获取银行卡详情", "parameters": {"query": [{"name": "id", "type": "integer", "description": "银行卡ID", "required": true}]}, "responses": [{"code": 200, "name": "成功"}]}}, {"name": "编辑银行卡", "api": {"method": "post", "path": "/shopapi/shop/editBank", "description": "编辑银行卡信息", "responses": [{"code": 200, "name": "成功"}]}}, {"name": "删除银行卡", "api": {"method": "post", "path": "/shopapi/shop/delBank", "description": "删除银行卡", "parameters": {"body": [{"name": "id", "type": "integer", "description": "银行卡ID", "required": true}]}, "responses": [{"code": 200, "name": "成功"}]}}]}, {"name": "密码管理", "id": ********, "parentId": ********, "items": [{"name": "修改密码", "api": {"method": "post", "path": "/shopapi/shop/changePwd", "description": "修改商家密码", "responses": [{"code": 200, "name": "成功"}]}}]}]}], "commonParameters": {"parameters": {"header": [{"name": "token", "type": "string", "description": "商家登录令牌", "required": true}]}}}