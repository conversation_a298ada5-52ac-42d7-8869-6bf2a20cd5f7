# 忘记密码功能优化说明

## 优化内容概述

本次优化主要针对kshop项目中的忘记密码功能进行了全面改进，包括shop端和shopapi端的逻辑优化、页面UI美化以及安全性增强。

## 主要改进点

### 1. 修复逻辑错误
- **修复了shop端PasswordResetLogic中的表名错误**：原代码引用了不存在的`shop_password_reset`表
- **优化了验证码验证流程**：使用现有的`sms_log`表进行验证码管理
- **改进了密码重置逻辑**：使用缓存机制管理重置令牌，提高安全性

### 2. 统一实现逻辑
- **创建了shopapi端的PasswordResetLogic**：与shop端保持一致的实现方式
- **统一了验证器规则**：两端使用相同的验证逻辑和错误提示
- **标准化了控制器接口**：确保API接口的一致性

### 3. 增强安全性
- **密码强度验证**：要求密码包含字母、数字或特殊字符中的至少两种
- **验证码有效期控制**：验证码5分钟有效期，重置令牌10分钟有效期
- **频率限制**：1分钟内只能发送一次验证码
- **令牌管理**：使用缓存管理重置令牌，使用后自动清除

### 4. 优化用户体验
- **美化了忘记密码弹窗**：使用更现代的UI设计，增加图标和提示信息
- **改进了交互反馈**：增加加载动画、倒计时显示、详细的错误提示
- **增强了表单验证**：前端实时验证手机号格式和密码强度

## 文件变更列表

### 新增文件
- `app/shopapi/logic/PasswordResetLogic.php` - shopapi端密码重置逻辑
- `app/shopapi/validate/PasswordResetValidate.php` - shopapi端验证器

### 修改文件
- `app/shop/logic/PasswordResetLogic.php` - 修复逻辑错误，优化实现
- `app/shop/validate/PasswordResetValidate.php` - 增强验证规则
- `app/shop/controller/PasswordReset.php` - 优化控制器逻辑
- `app/shopapi/controller/PasswordReset.php` - 统一接口实现
- `app/shop/view/login/login.html` - 优化UI和用户体验

## 功能测试指南

### 1. 发送验证码测试
**测试步骤：**
1. 访问商家登录页面
2. 点击"忘记密码？"链接
3. 输入已注册的手机号
4. 点击"发送验证码"按钮

**预期结果：**
- 手机收到6位数字验证码
- 按钮显示倒计时，60秒内不能重复发送
- 显示成功提示信息

### 2. 验证码验证测试
**测试步骤：**
1. 在忘记密码弹窗中输入收到的验证码
2. 设置新密码（需符合强度要求）
3. 确认新密码
4. 点击"重置密码"按钮

**预期结果：**
- 验证码正确时进入密码重置流程
- 密码强度不够时显示相应提示
- 重置成功后显示成功信息并关闭弹窗

### 3. 异常情况测试
**测试场景：**
- 输入未注册的手机号
- 输入错误的验证码
- 验证码过期（5分钟后）
- 密码强度不够
- 两次密码输入不一致

**预期结果：**
- 每种异常情况都有相应的错误提示
- 不会导致系统错误或崩溃

## API接口说明

### 发送验证码
- **URL**: `/shop/passwordReset/sendCode` 或 `/shopapi/passwordReset/sendCode`
- **方法**: POST
- **参数**:
  - `mobile`: 手机号
  - `key`: 短信模板key（默认SJZHMM，商家找回密码专用场景）
  - `client`: 客户端类型

### 验证验证码
- **URL**: `/shop/passwordReset/verifyCode` 或 `/shopapi/passwordReset/verifyCode`
- **方法**: POST
- **参数**:
  - `mobile`: 手机号
  - `code`: 验证码

### 重置密码
- **URL**: `/shop/passwordReset/resetPassword` 或 `/shopapi/passwordReset/resetPassword`
- **方法**: POST
- **参数**:
  - `token`: 重置令牌
  - `password`: 新密码
  - `confirm_password`: 确认密码

## 重要修复说明

### 短信场景修复
- **新增商家密码重置场景**：添加了`SJZHMM`场景专门用于商家找回密码
- **修复参数传递问题**：SmsLogic::send()方法需要三个参数：`$mobile`, `$scene`, `$user_id`
- **验证器优化**：SmsSend验证器新增对商家账号的验证逻辑

### 场景对比
- `ZHMM`：用户找回密码（检查ls_user表）
- `SJZHMM`：商家找回密码（检查shop表和shop_admin表）

## 注意事项

1. **数据库依赖**：功能依赖`ls_sms_log`表记录验证码信息
2. **缓存依赖**：使用Redis或文件缓存存储重置令牌
3. **短信服务**：需要配置短信发送服务
4. **密码加密**：使用`create_password()`和`create_salt()`函数进行密码加密
5. **场景配置**：需要在后台配置SJZHMM场景的短信模板

## 后续建议

1. **添加日志记录**：记录密码重置操作日志，便于安全审计
2. **增加图形验证码**：在发送短信验证码前增加图形验证码验证
3. **多渠道验证**：支持邮箱验证等多种验证方式
4. **安全策略**：增加IP限制、设备绑定等安全策略
