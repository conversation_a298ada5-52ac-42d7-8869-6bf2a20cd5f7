<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tsf\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * k8s env 的 ValueFrom
 *
 * @method FieldRef getFieldRef() 获取k8s env 的 FieldRef
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setFieldRef(FieldRef $FieldRef) 设置k8s env 的 FieldRef
注意：此字段可能返回 null，表示取不到有效值。
 * @method ResourceFieldRef getResourceFieldRef() 获取k8s env 的 ResourceFieldRef
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setResourceFieldRef(ResourceFieldRef $ResourceFieldRef) 设置k8s env 的 ResourceFieldRef
注意：此字段可能返回 null，表示取不到有效值。
 */
class ValueFrom extends AbstractModel
{
    /**
     * @var FieldRef k8s env 的 FieldRef
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $FieldRef;

    /**
     * @var ResourceFieldRef k8s env 的 ResourceFieldRef
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $ResourceFieldRef;

    /**
     * @param FieldRef $FieldRef k8s env 的 FieldRef
注意：此字段可能返回 null，表示取不到有效值。
     * @param ResourceFieldRef $ResourceFieldRef k8s env 的 ResourceFieldRef
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("FieldRef",$param) and $param["FieldRef"] !== null) {
            $this->FieldRef = new FieldRef();
            $this->FieldRef->deserialize($param["FieldRef"]);
        }

        if (array_key_exists("ResourceFieldRef",$param) and $param["ResourceFieldRef"] !== null) {
            $this->ResourceFieldRef = new ResourceFieldRef();
            $this->ResourceFieldRef->deserialize($param["ResourceFieldRef"]);
        }
    }
}
