<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Iotvideo\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * TransferCloudStorage请求参数结构体
 *
 * @method string getProductId() 获取产品ID
 * @method void setProductId(string $ProductId) 设置产品ID
 * @method string getDeviceName() 获取已开通云存的设备名称
 * @method void setDeviceName(string $DeviceName) 设置已开通云存的设备名称
 * @method string getToDeviceName() 获取未开通云存的设备名称
 * @method void setToDeviceName(string $ToDeviceName) 设置未开通云存的设备名称
 */
class TransferCloudStorageRequest extends AbstractModel
{
    /**
     * @var string 产品ID
     */
    public $ProductId;

    /**
     * @var string 已开通云存的设备名称
     */
    public $DeviceName;

    /**
     * @var string 未开通云存的设备名称
     */
    public $ToDeviceName;

    /**
     * @param string $ProductId 产品ID
     * @param string $DeviceName 已开通云存的设备名称
     * @param string $ToDeviceName 未开通云存的设备名称
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ProductId",$param) and $param["ProductId"] !== null) {
            $this->ProductId = $param["ProductId"];
        }

        if (array_key_exists("DeviceName",$param) and $param["DeviceName"] !== null) {
            $this->DeviceName = $param["DeviceName"];
        }

        if (array_key_exists("ToDeviceName",$param) and $param["ToDeviceName"] !== null) {
            $this->ToDeviceName = $param["ToDeviceName"];
        }
    }
}
