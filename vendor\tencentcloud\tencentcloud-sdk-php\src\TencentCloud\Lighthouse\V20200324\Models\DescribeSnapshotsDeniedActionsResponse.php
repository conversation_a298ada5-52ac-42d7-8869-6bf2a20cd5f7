<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Lighthouse\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeSnapshotsDeniedActions返回参数结构体
 *
 * @method array getSnapshotDeniedActionSet() 获取快照操作限制列表详细信息。
 * @method void setSnapshotDeniedActionSet(array $SnapshotDeniedActionSet) 设置快照操作限制列表详细信息。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeSnapshotsDeniedActionsResponse extends AbstractModel
{
    /**
     * @var array 快照操作限制列表详细信息。
     */
    public $SnapshotDeniedActionSet;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $SnapshotDeniedActionSet 快照操作限制列表详细信息。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SnapshotDeniedActionSet",$param) and $param["SnapshotDeniedActionSet"] !== null) {
            $this->SnapshotDeniedActionSet = [];
            foreach ($param["SnapshotDeniedActionSet"] as $key => $value){
                $obj = new SnapshotDeniedActions();
                $obj->deserialize($value);
                array_push($this->SnapshotDeniedActionSet, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
