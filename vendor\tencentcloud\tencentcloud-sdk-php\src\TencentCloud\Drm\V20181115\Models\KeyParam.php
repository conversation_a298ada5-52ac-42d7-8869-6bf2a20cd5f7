<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Drm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 设置加密密钥所需的参数
 *
 * @method string getTrack() 获取加密track类型。取值范围：
SD、HD、UHD1、UHD2、AUDIO
 * @method void setTrack(string $Track) 设置加密track类型。取值范围：
SD、HD、UHD1、UHD2、AUDIO
 * @method string getKey() 获取请使用腾讯云DRM 提供的公钥，使用rsa加密算法，PKCS1填充方式对解密密钥进行加密，并对加密结果进行base64编码。
 * @method void setKey(string $Key) 设置请使用腾讯云DRM 提供的公钥，使用rsa加密算法，PKCS1填充方式对解密密钥进行加密，并对加密结果进行base64编码。
 * @method string getKeyId() 获取密钥ID。
 * @method void setKeyId(string $KeyId) 设置密钥ID。
 * @method string getIv() 获取请使用腾讯云DRM 提供的公钥，使用rsa加密算法，PKCS1填充方式对解密密钥进行加密，并对加密结果进行base64编码。
 * @method void setIv(string $Iv) 设置请使用腾讯云DRM 提供的公钥，使用rsa加密算法，PKCS1填充方式对解密密钥进行加密，并对加密结果进行base64编码。
 */
class KeyParam extends AbstractModel
{
    /**
     * @var string 加密track类型。取值范围：
SD、HD、UHD1、UHD2、AUDIO
     */
    public $Track;

    /**
     * @var string 请使用腾讯云DRM 提供的公钥，使用rsa加密算法，PKCS1填充方式对解密密钥进行加密，并对加密结果进行base64编码。
     */
    public $Key;

    /**
     * @var string 密钥ID。
     */
    public $KeyId;

    /**
     * @var string 请使用腾讯云DRM 提供的公钥，使用rsa加密算法，PKCS1填充方式对解密密钥进行加密，并对加密结果进行base64编码。
     */
    public $Iv;

    /**
     * @param string $Track 加密track类型。取值范围：
SD、HD、UHD1、UHD2、AUDIO
     * @param string $Key 请使用腾讯云DRM 提供的公钥，使用rsa加密算法，PKCS1填充方式对解密密钥进行加密，并对加密结果进行base64编码。
     * @param string $KeyId 密钥ID。
     * @param string $Iv 请使用腾讯云DRM 提供的公钥，使用rsa加密算法，PKCS1填充方式对解密密钥进行加密，并对加密结果进行base64编码。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Track",$param) and $param["Track"] !== null) {
            $this->Track = $param["Track"];
        }

        if (array_key_exists("Key",$param) and $param["Key"] !== null) {
            $this->Key = $param["Key"];
        }

        if (array_key_exists("KeyId",$param) and $param["KeyId"] !== null) {
            $this->KeyId = $param["KeyId"];
        }

        if (array_key_exists("Iv",$param) and $param["Iv"] !== null) {
            $this->Iv = $param["Iv"];
        }
    }
}
