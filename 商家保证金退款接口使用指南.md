# 商家保证金退款接口使用指南

## 概述

本文档提供商家保证金退款功能的完整使用指南，包括接口调用流程、参数说明、错误处理等。

## 快速开始

### 1. 导入 Apifox 文档

1. 打开 Apifox 应用
2. 选择"导入" -> "导入数据"
3. 选择 `商家保证金退款接口.apifox.json` 文件
4. 确认导入设置并完成导入

### 2. 配置环境

在 Apifox 中配置以下环境变量：

```json
{
  "baseUrl": "https://www.huohanghang.cn",
  "token": "your_shop_token_here"
}
```

## 接口调用流程

### 标准退款流程

```mermaid
sequenceDiagram
    participant Client as 前端应用
    participant API as 后端接口
    participant DB as 数据库

    Client->>API: 1. 获取退款警告信息
    API-->>Client: 返回警告图片和说明

    Client->>API: 2. 检查退款资格
    API->>DB: 查询订单、商品、售后状态
    API-->>Client: 返回检查结果

    alt 满足退款条件
        Client->>API: 3. 确认退款申请
        API->>DB: 更新保证金状态
        API-->>Client: 返回申请成功
    else 不满足条件
        Client-->>Client: 显示不满足条件的详细信息
    end
```

## 接口详细说明

### 1. 获取退款警告信息

**接口路径：** `GET /shopapi/index/getShopDepositRefundNotice`

**功能说明：**
- 获取后台配置的退款警告图片
- 获取退款公示期天数配置
- 无需特殊参数，但需要商家登录状态

**调用示例：**
```bash
curl -X GET "https://www.huohanghang.cn/shopapi/index/getShopDepositRefundNotice" \
  -H "token: your_shop_token"
```

**响应示例：**
```json
{
  "code": 1,
  "msg": "获取成功",
  "data": {
    "notice": "<div><img src='/uploads/warning.png' /></div>",
    "publicity_period_days": 7
  }
}
```

### 2. 检查退款资格

**接口路径：** `GET /shopapi/index/checkShopDepositRefundCondition`

**功能说明：**
- 检查保证金记录是否有效
- 检查是否有未完成的订单
- 检查是否有上架的商品
- 检查是否有维权期内的订单
- 检查是否有未处理的售后

**检查项目详解：**

| 检查项 | 字段名 | 说明 |
|--------|--------|------|
| 保证金状态 | has_deposit | 必须有已审核通过且未申请退款的保证金记录 |
| 进行中订单 | ongoing_orders | 不能有待发货、待收货等未完成订单 |
| 上架商品 | active_goods | 所有商品必须已下架 |
| 维权期检查 | rights_protection | 确认收货后15天内的订单仍在维权期 |
| 未发货订单 | unshipped_orders | 不能有待发货或待收货的订单 |
| 待处理售后 | pending_aftersales | 所有售后必须已处理完成 |

**调用示例：**
```bash
curl -X GET "https://www.huohanghang.cn/shopapi/index/checkShopDepositRefundCondition" \
  -H "token: your_shop_token"
```

### 3. 确认退款申请

**接口路径：** `POST /shopapi/index/confirmShopDepositRefund`

**功能说明：**
- 在满足所有条件后提交退款申请
- 自动重新检查退款条件
- 更新保证金状态为"申请中"

**请求参数：**
```json
{
  "reason": "商家主动申请退款"  // 可选，退款原因
}
```

**调用示例：**
```bash
curl -X POST "https://www.huohanghang.cn/shopapi/index/confirmShopDepositRefund" \
  -H "token: your_shop_token" \
  -H "Content-Type: application/json" \
  -d '{"reason": "商家主动申请退款"}'
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 0 | 请求失败 | 检查请求参数和业务逻辑 |
| 401 | 未登录 | 检查token是否有效 |
| 403 | 权限不足 | 确认商家账号权限 |
| 500 | 服务器错误 | 联系技术支持 |

### 业务错误处理

```javascript
// 检查退款条件的错误处理示例
async function handleRefundCheck(response) {
  if (response.code === 1) {
    if (response.data.can_deactivate) {
      // 可以申请退款
      showConfirmButton();
    } else {
      // 不满足条件，显示详细信息
      showConditionDetails(response.data);
    }
  } else {
    // 接口调用失败
    showError(response.msg);
  }
}

function showConditionDetails(data) {
  const conditions = [
    'has_deposit', 'ongoing_orders', 'active_goods',
    'rights_protection', 'unshipped_orders', 'pending_aftersales'
  ];
  
  conditions.forEach(condition => {
    if (data[condition] && !data[condition].status) {
      // 显示不满足的条件
      console.log(`不满足条件: ${data[condition].message}`);
    }
  });
}
```

## 前端集成建议

### 1. 用户体验优化

- **步骤指引**：清晰显示退款申请的步骤
- **条件检查**：实时显示各项检查结果
- **警告提示**：突出显示退款警告信息
- **进度反馈**：显示申请处理进度

### 2. 界面设计建议

```html
<!-- 退款申请页面结构建议 -->
<div class="refund-application">
  <!-- 警告信息区域 -->
  <div class="warning-section">
    <div id="warning-content"></div>
  </div>
  
  <!-- 条件检查区域 -->
  <div class="condition-check">
    <div class="condition-item" id="has_deposit">
      <span class="condition-name">保证金状态</span>
      <span class="condition-status"></span>
      <span class="condition-message"></span>
    </div>
    <!-- 其他检查项... -->
  </div>
  
  <!-- 操作区域 -->
  <div class="action-section">
    <textarea placeholder="请输入退款原因（可选）"></textarea>
    <button id="confirm-refund" disabled>确认申请退款</button>
  </div>
</div>
```

### 3. 状态管理

```javascript
// 退款申请状态管理
const refundState = {
  warningLoaded: false,
  conditionsChecked: false,
  canRefund: false,
  conditions: {},
  
  // 更新状态
  updateConditions(data) {
    this.conditions = data;
    this.canRefund = data.can_deactivate;
    this.conditionsChecked = true;
  },
  
  // 检查是否可以提交申请
  canSubmit() {
    return this.warningLoaded && 
           this.conditionsChecked && 
           this.canRefund;
  }
};
```

## 测试建议

### 1. 功能测试

- 测试各种业务状态下的退款条件检查
- 测试退款申请的完整流程
- 测试错误情况的处理

### 2. 边界测试

- 测试保证金余额为0的情况
- 测试重复申请的处理
- 测试并发请求的处理

### 3. 性能测试

- 测试大量数据下的检查性能
- 测试高并发情况下的接口稳定性

## 注意事项

1. **权限验证**：所有接口都需要有效的商家登录token
2. **数据一致性**：退款申请使用数据库事务保证数据一致性
3. **业务规则**：严格按照业务规则进行退款条件检查
4. **用户体验**：提供清晰的错误提示和操作指引
5. **安全性**：验证商家身份和操作权限

## 技术支持

如有问题，请联系技术支持团队或查看相关文档：

- 接口文档：`商家保证金退款接口文档.md`
- Apifox文档：`商家保证金退款接口.apifox.json`
- 代码实现：`app/shopapi/controller/Index.php`
