<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ecm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 镜像限制配置
 *
 * @method integer getMaxImageSize() 获取支持的最大镜像大小，包括可导入的自定义镜像大小，中心云镜像大小，单位为GB。
 * @method void setMaxImageSize(integer $MaxImageSize) 设置支持的最大镜像大小，包括可导入的自定义镜像大小，中心云镜像大小，单位为GB。
 */
class ImageLimitConfig extends AbstractModel
{
    /**
     * @var integer 支持的最大镜像大小，包括可导入的自定义镜像大小，中心云镜像大小，单位为GB。
     */
    public $MaxImageSize;

    /**
     * @param integer $MaxImageSize 支持的最大镜像大小，包括可导入的自定义镜像大小，中心云镜像大小，单位为GB。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("MaxImageSize",$param) and $param["MaxImageSize"] !== null) {
            $this->MaxImageSize = $param["MaxImageSize"];
        }
    }
}
