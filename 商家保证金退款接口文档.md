# 商家保证金退款接口文档

## 概述

本文档描述了商家保证金退款功能的三个核心接口，参照代理保证金退款接口的实现模式，提供完整的退款流程支持。

## 接口列表

### 1. 获取商家保证金退款警告信息

**接口地址：** `GET /shopapi/index/getShopDepositRefundNotice`

**接口描述：** 获取商家保证金退款的警告信息，包括警告图片和相关说明

**请求参数：** 无

**返回示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "notice": "<div><img src=\"/uploads/shop_deposit_refund_warning.png\" /></div>",
        "publicity_period_days": 7
    }
}
```

**返回参数说明：**
- `notice`: 警告信息HTML内容，包含警告图片
- `publicity_period_days`: 公示期天数

---

### 2. 检查商家保证金退款资格

**接口地址：** `GET /shopapi/index/checkShopDepositRefundCondition`

**接口描述：** 检查商家是否满足保证金退款条件，包括订单状态、商品状态、维权期等多项检查

**请求参数：** 无（需要登录，自动获取商家ID）

**返回示例：**
```json
{
    "code": 1,
    "show": 0,
    "msg": "商家保证金退款条件检查结果",
    "data": {
        "can_deactivate": false,
        "has_deposit": {
            "status": true,
            "message": "保证金状态有效，当前余额：5000.00元"
        },
        "ongoing_orders": {
            "status": false,
            "message": "您有 3 个订单未完成，请先处理完成"
        },
        "active_goods": {
            "status": false,
            "message": "您有 15 个商品仍在上架状态，请先下架所有商品"
        },
        "rights_protection": {
            "status": false,
            "message": "您有 2 个订单仍在维权期内（确认收货后15天），请等待维权期结束"
        },
        "unshipped_orders": {
            "status": true,
            "message": "所有订单已发货并收货"
        },
        "pending_aftersales": {
            "status": true,
            "message": "所有售后已处理完成"
        }
    }
}
```

**检查项目说明：**
- `has_deposit`: 保证金记录检查
- `ongoing_orders`: 进行中的订单检查
- `active_goods`: 上架商品检查
- `rights_protection`: 维权期检查（确认收货后15天内）
- `unshipped_orders`: 未发货订单检查
- `pending_aftersales`: 未处理售后检查

**状态码说明：**
- `can_deactivate`: true表示可以申请退款，false表示不满足条件
- 每个检查项的`status`: true表示通过，false表示不通过

---

### 3. 确认商家保证金退款申请

**接口地址：** `POST /shopapi/index/confirmShopDepositRefund`

**接口描述：** 在通过所有检查后，提交保证金退款申请

**请求参数：**
```json
{
    "reason": "商家主动申请退款"
}
```

**参数说明：**
- `reason`: 退款原因（可选，默认为"商家申请退款"）

**成功返回示例：**
```json
{
    "code": 1,
    "show": 0,
    "msg": "退款申请提交成功，请等待平台审核",
    "data": {
        "can_deactivate": true,
        "refund_amount": "5000.00"
    }
}
```

**失败返回示例：**
```json
{
    "code": 0,
    "show": 1,
    "msg": "申请失败：您有 3 个订单未完成，请先处理完成",
    "data": {
        "can_deactivate": false,
        "has_deposit": {
            "status": true,
            "message": "保证金状态有效，当前余额：5000.00元"
        },
        "ongoing_orders": {
            "status": false,
            "message": "您有 3 个订单未完成，请先处理完成"
        }
    }
}
```

## 业务流程

### 退款申请流程

1. **获取警告信息**
   - 调用 `getShopDepositRefundNotice` 接口
   - 展示退款警告图片和相关说明

2. **检查退款资格**
   - 调用 `checkShopDepositRefundCondition` 接口
   - 检查所有退款条件
   - 如果不满足条件，显示具体原因

3. **提交退款申请**
   - 在满足所有条件后，调用 `confirmShopDepositRefund` 接口
   - 提交退款申请，等待平台审核

### 退款条件详解

#### 1. 保证金状态检查
- 必须有已审核通过的保证金记录
- 保证金状态必须为未申请退款
- 保证金余额必须大于0

#### 2. 订单状态检查
- 所有订单必须已完成或已关闭
- 不能有待发货、待收货的订单
- 已完成订单必须过了维权期（确认收货后15天）

#### 3. 商品状态检查
- 所有商品必须已下架
- 不能有正在销售的商品

#### 4. 售后处理检查
- 所有售后订单必须已处理完成
- 不能有待处理的售后申请

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 请求失败 |
| 1 | 请求成功 |

## 注意事项

1. **权限验证**：所有接口都需要商家登录状态
2. **数据安全**：退款金额计算基于保证金记录和变动明细
3. **业务逻辑**：严格按照业务规则检查退款条件
4. **事务处理**：确认退款接口使用数据库事务保证数据一致性
5. **错误处理**：提供详细的错误信息帮助商家了解不满足条件的具体原因

## 配置项

### 后台配置
- `shop_entry.shop_deposit_refund_warning_image`: 退款警告图片
- `shop_entry.refund_publicity_period_days`: 退款公示期天数（默认7天）

### 数据库表
- `ls_shop_deposit`: 商家保证金主表
- `ls_shop_deposit_details`: 保证金变动明细表
- `ls_order`: 订单表
- `ls_goods`: 商品表
- `ls_after_sale`: 售后表

## 前端集成指南

### JavaScript 示例

```javascript
// 1. 获取退款警告信息
async function getRefundNotice() {
    try {
        const response = await fetch('/shopapi/index/getShopDepositRefundNotice', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        if (data.code === 1) {
            // 显示警告信息
            document.getElementById('warning-content').innerHTML = data.data.notice;
        }
    } catch (error) {
        console.error('获取警告信息失败:', error);
    }
}

// 2. 检查退款资格
async function checkRefundCondition() {
    try {
        const response = await fetch('/shopapi/index/checkShopDepositRefundCondition', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();

        if (data.code === 1) {
            const canRefund = data.data.can_deactivate;

            if (canRefund) {
                // 显示确认退款按钮
                document.getElementById('confirm-btn').style.display = 'block';
            } else {
                // 显示不满足条件的详细信息
                showConditionDetails(data.data);
            }
        }
    } catch (error) {
        console.error('检查退款条件失败:', error);
    }
}

// 3. 确认退款申请
async function confirmRefund(reason) {
    try {
        const response = await fetch('/shopapi/index/confirmShopDepositRefund', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                reason: reason || '商家主动申请退款'
            })
        });
        const data = await response.json();

        if (data.code === 1) {
            alert('退款申请提交成功，退款金额：' + data.data.refund_amount + '元');
        } else {
            alert('申请失败：' + data.msg);
        }
    } catch (error) {
        console.error('确认退款失败:', error);
    }
}

// 显示条件检查详情
function showConditionDetails(data) {
    const conditions = [
        'has_deposit', 'ongoing_orders', 'active_goods',
        'rights_protection', 'unshipped_orders', 'pending_aftersales'
    ];

    conditions.forEach(condition => {
        if (data[condition]) {
            const element = document.getElementById(condition + '-status');
            if (element) {
                element.className = data[condition].status ? 'success' : 'error';
                element.textContent = data[condition].message;
            }
        }
    });
}
```

### Vue.js 示例

```vue
<template>
  <div class="refund-deposit">
    <!-- 警告信息 -->
    <div class="warning-section">
      <div v-html="warningNotice"></div>
    </div>

    <!-- 条件检查 -->
    <div class="condition-check">
      <div v-for="(condition, key) in conditions" :key="key"
           :class="['condition-item', condition.status ? 'success' : 'error']">
        <span class="condition-name">{{ getConditionName(key) }}</span>
        <span class="condition-message">{{ condition.message }}</span>
      </div>
    </div>

    <!-- 确认按钮 -->
    <div class="action-section" v-if="canRefund">
      <el-input v-model="refundReason" placeholder="请输入退款原因（可选）"></el-input>
      <el-button type="primary" @click="confirmRefund">确认申请退款</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      warningNotice: '',
      conditions: {},
      canRefund: false,
      refundReason: ''
    }
  },

  async mounted() {
    await this.getRefundNotice();
    await this.checkRefundCondition();
  },

  methods: {
    async getRefundNotice() {
      try {
        const { data } = await this.$http.get('/shopapi/index/getShopDepositRefundNotice');
        if (data.code === 1) {
          this.warningNotice = data.data.notice;
        }
      } catch (error) {
        this.$message.error('获取警告信息失败');
      }
    },

    async checkRefundCondition() {
      try {
        const { data } = await this.$http.get('/shopapi/index/checkShopDepositRefundCondition');
        if (data.code === 1) {
          this.canRefund = data.data.can_deactivate;
          this.conditions = data.data;
        }
      } catch (error) {
        this.$message.error('检查退款条件失败');
      }
    },

    async confirmRefund() {
      try {
        const { data } = await this.$http.post('/shopapi/index/confirmShopDepositRefund', {
          reason: this.refundReason || '商家主动申请退款'
        });

        if (data.code === 1) {
          this.$message.success(`退款申请提交成功，退款金额：${data.data.refund_amount}元`);
        } else {
          this.$message.error(data.msg);
        }
      } catch (error) {
        this.$message.error('确认退款失败');
      }
    },

    getConditionName(key) {
      const names = {
        has_deposit: '保证金状态',
        ongoing_orders: '进行中订单',
        active_goods: '上架商品',
        rights_protection: '维权期检查',
        unshipped_orders: '未发货订单',
        pending_aftersales: '待处理售后'
      };
      return names[key] || key;
    }
  }
}
</script>
```

## 测试用例

### 测试环境准备

1. **测试商家账号**：需要有已缴纳保证金的商家账号
2. **测试数据**：准备不同状态的订单、商品、售后数据
3. **权限配置**：确保测试账号有相应的接口访问权限

### 功能测试用例

#### 用例1：正常退款流程
```bash
# 1. 获取警告信息
curl -X GET "https://www.huohanghang.cn/shopapi/index/getShopDepositRefundNotice" \
  -H "Authorization: Bearer {token}"

# 2. 检查退款条件（满足所有条件）
curl -X GET "https://www.huohanghang.cn/shopapi/index/checkShopDepositRefundCondition" \
  -H "Authorization: Bearer {token}"

# 3. 确认退款申请
curl -X POST "https://www.huohanghang.cn/shopapi/index/confirmShopDepositRefund" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{"reason": "测试退款申请"}'
```

#### 用例2：不满足退款条件
```bash
# 检查退款条件（有未完成订单）
curl -X GET "https://www.huohanghang.cn/shopapi/index/checkShopDepositRefundCondition" \
  -H "Authorization: Bearer {token}"

# 预期返回：can_deactivate: false，并显示具体不满足的条件
```

#### 用例3：未登录状态
```bash
# 未提供token的请求
curl -X GET "https://www.huohanghang.cn/shopapi/index/checkShopDepositRefundCondition"

# 预期返回：请先登录
```

### 边界测试用例

1. **保证金余额为0**：测试余额不足的情况
2. **重复申请**：测试已申请退款后再次申请
3. **并发请求**：测试同时多次提交申请
4. **数据异常**：测试数据库异常情况的处理

## 版本信息

- **版本**: v1.0
- **创建时间**: 2024-12-18
- **最后更新**: 2024-12-18
- **作者**: AI Assistant
- **参考**: 代理保证金退款接口实现
