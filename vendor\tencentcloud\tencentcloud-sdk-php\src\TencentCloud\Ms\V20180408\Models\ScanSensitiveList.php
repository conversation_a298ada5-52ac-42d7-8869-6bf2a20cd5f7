<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ms\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 安全扫描敏感词列表
 *
 * @method array getSensitiveList() 获取敏感词列表
 * @method void setSensitiveList(array $SensitiveList) 设置敏感词列表
 */
class ScanSensitiveList extends AbstractModel
{
    /**
     * @var array 敏感词列表
     */
    public $SensitiveList;

    /**
     * @param array $SensitiveList 敏感词列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SensitiveList",$param) and $param["SensitiveList"] !== null) {
            $this->SensitiveList = [];
            foreach ($param["SensitiveList"] as $key => $value){
                $obj = new ScanSensitiveInfo();
                $obj->deserialize($value);
                array_push($this->SensitiveList, $obj);
            }
        }
    }
}
