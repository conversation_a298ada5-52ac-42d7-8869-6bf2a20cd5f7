<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tsf\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ChangeApiUsableStatus请求参数结构体
 *
 * @method string getApiId() 获取API ID
 * @method void setApiId(string $ApiId) 设置API ID
 * @method string getUsableStatus() 获取切换状态，enabled/disabled
 * @method void setUsableStatus(string $UsableStatus) 设置切换状态，enabled/disabled
 */
class ChangeApiUsableStatusRequest extends AbstractModel
{
    /**
     * @var string API ID
     */
    public $ApiId;

    /**
     * @var string 切换状态，enabled/disabled
     */
    public $UsableStatus;

    /**
     * @param string $ApiId API ID
     * @param string $UsableStatus 切换状态，enabled/disabled
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ApiId",$param) and $param["ApiId"] !== null) {
            $this->ApiId = $param["ApiId"];
        }

        if (array_key_exists("UsableStatus",$param) and $param["UsableStatus"] !== null) {
            $this->UsableStatus = $param["UsableStatus"];
        }
    }
}
