{layout name="layout1" /}

<!-- 页面样式 -->
<style>
.qualification-upload-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.qualification-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 8px 8px 0 0;
    text-align: center;
    position: relative;
}

.qualification-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px 8px 0 0;
}

.qualification-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.qualification-header p {
    margin: 8px 0 0 0;
    font-size: 14px;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.qualification-info-card {
    background: #f8fffe;
    border: 1px solid #d4edda;
    border-radius: 6px;
    padding: 16px;
    margin: 20px 0;
    position: relative;
}

.qualification-info-card::before {
    content: '\e705';
    font-family: 'layui-icon';
    position: absolute;
    top: 16px;
    left: 16px;
    color: #28a745;
    font-size: 16px;
}

.qualification-info-content {
    margin-left: 30px;
}

.qualification-description {
    color: #495057;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 12px;
}

.qualification-description strong {
    color: #28a745;
    font-weight: 600;
}

.template-download-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: linear-gradient(45deg, #1890ff, #40a9ff);
    border: none;
    border-radius: 20px;
    color: white;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.template-download-btn:hover {
    background: linear-gradient(45deg, #096dd9, #1890ff);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
    color: white;
    text-decoration: none;
}

.template-download-btn i {
    margin-right: 6px;
    font-size: 14px;
}

/* 示例按钮样式 */
.qualification-example-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: linear-gradient(45deg, #52c41a, #73d13d);
    border: none;
    border-radius: 20px;
    color: white;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
}

.qualification-example-btn:hover {
    background: linear-gradient(45deg, #389e0d, #52c41a);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(82, 196, 26, 0.4);
}

.qualification-example-btn i {
    margin-right: 5px;
}

.upload-form-container {
    background: white;
    border-radius: 0 0 8px 8px;
    padding: 30px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-section {
    margin-bottom: 25px;
}

.form-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
}

.form-section-title::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    padding: 30px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    position: relative;
}

.upload-area:hover {
    border-color: #1890ff;
    background: #f6ffed;
}

.upload-area.has-file {
    border-color: #52c41a;
    background: #f6ffed;
}

.upload-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
    color: #1890ff;
}

.upload-area.has-file .upload-icon {
    color: #52c41a;
}

.upload-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
}

.upload-hint {
    font-size: 12px;
    color: #999;
    line-height: 1.5;
}

.upload-btn {
    background: linear-gradient(45deg, #1890ff, #40a9ff);
    border: none;
    border-radius: 20px;
    padding: 10px 24px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.upload-btn:hover {
    background: linear-gradient(45deg, #096dd9, #1890ff);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

.file-list {
    margin-top: 15px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #52c41a;
}

.file-item {
    display: flex;
    align-items: center;
    color: #52c41a;
    font-size: 14px;
}

.file-item i {
    margin-right: 8px;
    font-size: 16px;
}

.file-remove {
    margin-left: auto;
    color: #ff4d4f;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.file-remove:hover {
    background: #fff2f0;
}

.submit-area {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.submit-btn {
    background: linear-gradient(45deg, #52c41a, #73d13d);
    border: none;
    border-radius: 25px;
    padding: 12px 40px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
    min-width: 120px;
}

.submit-btn:hover {
    background: linear-gradient(45deg, #389e0d, #52c41a);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
}

.cancel-btn {
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 25px;
    padding: 12px 30px;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 15px;
}

.cancel-btn:hover {
    background: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
}

.layui-form-label {
    font-weight: 600;
    color: #333;
}

.layui-input {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s ease;
}

.layui-input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 上传状态样式 */
.upload-area.uploading {
    border-color: #1890ff;
    background: #f0f9ff;
}

.upload-area.uploading .upload-icon {
    color: #1890ff;
}

.upload-area.uploading .upload-text {
    color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .qualification-upload-container {
        padding: 10px;
    }

    .qualification-header {
        padding: 20px 15px;
    }

    .qualification-header h3 {
        font-size: 18px;
    }

    .upload-form-container {
        padding: 20px 15px;
    }

    .submit-btn, .cancel-btn {
        width: 100%;
        margin: 5px 0;
    }

    .cancel-btn {
        margin-right: 0;
    }
}

/* 加载动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.upload-area.uploading {
    animation: pulse 2s infinite;
}
</style>

<div class="qualification-upload-container">
    <div class="qualification-header">
        <h3><i class="layui-icon layui-icon-upload-circle" style="margin-right: 8px;"></i>上传资质：{$qualification_name}</h3>
        <p>该分类需要上传此资质才能发布商品</p>
    </div>

    {if $qualification}
    <!-- 资质信息显示 -->
    <div class="qualification-info-card">
        <div class="qualification-info-content">
            {if $qualification.description}
            <div class="qualification-description">
                <strong>资质说明：</strong>{$qualification.description}
            </div>
            {/if}

            {if $qualification.document_path && $qualification.document_name}
            <div>
                <a href="{$qualification.document_path}" target="_blank" class="template-download-btn">
                    <i class="layui-icon layui-icon-download-circle"></i>下载承诺书模板
                </a>
            </div>
            {/if}

            {if $qualification.ex_img}
            <div style="margin-top: 10px;">
                <button type="button" class="qualification-example-btn" onclick="showExampleImage('{$qualification.ex_img}', '{$qualification.name}')">
                    <i class="layui-icon layui-icon-picture"></i>查看示例
                </button>
            </div>
            {/if}
        </div>
    </div>
    {/if}

    <div class="upload-form-container">
        <form class="layui-form" lay-filter="quick-upload-form">
            <input type="hidden" name="qualification_id" value="{$qualification_id}">

            <!-- 图片上传区域 -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="layui-icon layui-icon-picture" style="margin-right: 8px;"></i>资质图片
                </div>

                <div class="upload-area" id="upload-area">
                    <div class="upload-icon">
                        <i class="layui-icon layui-icon-upload-drag"></i>
                    </div>
                    <div class="upload-text">点击选择图片或拖拽图片到此处</div>
                    <div class="upload-hint">
                        支持格式：JPG、JPEG、PNG、GIF、BMP等图片格式<br>
                        文件大小不超过5MB
                    </div>
                    <button type="button" class="upload-btn" id="upload-document">
                        <i class="layui-icon layui-icon-picture"></i> 选择图片
                    </button>

                    <div id="document-list" class="file-list" style="display: none;">
                        <!-- 图片预览将在这里显示 -->
                    </div>

                    <input type="hidden" name="document_path" id="document_path" lay-verify="required">
                    <input type="hidden" name="document_name" id="document_name">
                </div>
            </div>

            <!-- 有效期设置 -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="layui-icon layui-icon-date" style="margin-right: 8px;"></i>有效期设置
                </div>

                <div class="layui-form-item" style="display: none;">
                    <label class="layui-form-label">有效期</label>
                    <div class="layui-input-block">
                        <input type="text" name="expire_time" placeholder="请选择有效期（可选）" class="layui-input" id="expire-time">
                        <div class="layui-form-mid layui-word-aux">
                            <i class="layui-icon layui-icon-tips" style="color: #1890ff; margin-right: 4px;"></i>
                            不选择表示永久有效
                        </div>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="submit-area">
                <button type="button" class="cancel-btn" onclick="parent.layer.closeAll()">
                    <i class="layui-icon layui-icon-close" style="margin-right: 4px;"></i>取消
                </button>
                <button class="submit-btn" lay-submit lay-filter="submit">
                    <i class="layui-icon layui-icon-ok" style="margin-right: 6px;"></i>立即提交
                </button>
            </div>
        </form>
    </div>
</div>

<script src="__PUBLIC__/static/lib/layui/layui.js?v=2.8.1.20240830"></script>
<script>

layui.use(['form', 'upload', 'laydate', 'layer'], function(){
    var form = layui.form;
    var upload = layui.upload;
    var laydate = layui.laydate;
    var layer = layui.layer;
    var $ = layui.$;

    // 日期选择器
    laydate.render({
        elem: '#expire-time',
        type: 'datetime',
        min: 0 // 不能选择今天之前的日期
    });

    // 图片上传
    upload.render({
        elem: '#upload-document',
        url: '{:url("Upload/document")}',
        accept: 'images',
        exts: 'jpg|jpeg|png|gif|bmp',
        size: 5120, // 5MB
        before: function(obj){
            // 显示上传中状态
            var uploadArea = $('#upload-area');
            uploadArea.addClass('uploading');
            uploadArea.find('.upload-text').text('正在上传...');
            uploadArea.find('.upload-icon i').removeClass('layui-icon-upload-drag').addClass('layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop');
        },
        done: function(res){
            var uploadArea = $('#upload-area');
            uploadArea.removeClass('uploading');

            if(res.code === 1) {
                $('#document_path').val(res.data.uri);
                $('#document_name').val(res.data.name);

                // 更新上传区域状态
                uploadArea.addClass('has-file');
                uploadArea.find('.upload-text').text('图片上传成功');
                uploadArea.find('.upload-icon i').removeClass('layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop').addClass('layui-icon-ok');

                // 后端已经返回完整URL，直接使用
                var imageUrl = res.data.uri;

                // 显示图片预览
                $('#document-list').show().html(
                    '<div class="file-item">' +
                        '<i class="layui-icon layui-icon-picture"></i>' +
                        '<span>' + res.data.name + '</span>' +
                        '<span class="file-remove" onclick="removeDocument()">' +
                            '<i class="layui-icon layui-icon-close"></i>' +
                        '</span>' +
                    '</div>' +
                    '<div style="margin-top: 10px;">' +
                        '<img src="' + imageUrl + '" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';">' +
                        '<div style="display:none; color: #999; text-align: center; padding: 20px; border: 1px dashed #ddd;">图片加载失败</div>' +
                    '</div>'
                );

                layer.msg('图片上传成功', {icon: 1});
            } else {
                // 恢复初始状态
                uploadArea.find('.upload-text').text('点击选择图片或拖拽图片到此处');
                uploadArea.find('.upload-icon i').removeClass('layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop').addClass('layui-icon-upload-drag');
                layer.msg(res.msg || '上传失败', {icon: 2});
            }
        },
        error: function(){
            var uploadArea = $('#upload-area');
            uploadArea.removeClass('uploading');
            uploadArea.find('.upload-text').text('点击选择图片或拖拽图片到此处');
            uploadArea.find('.upload-icon i').removeClass('layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop').addClass('layui-icon-upload-drag');
            layer.msg('上传失败', {icon: 2});
        }
    });

    // 表单提交
    form.on('submit(submit)', function(data){
        var loadIndex = layer.load(2);

        $.post('{:url("shop_qualification/quickUpload")}', data.field, function(res){
            layer.close(loadIndex);
            if(res.code === 1){
                layer.msg('上传成功', {icon: 1}, function(){
                    // 通知父窗口刷新
                    if(parent && parent.window && parent.window.refreshQualificationCheck) {
                        parent.window.refreshQualificationCheck();
                    }
                    // 刷新父页面的表格（如果存在）
                    if (parent.layui && parent.layui.table) {
                        parent.layui.table.reload('qualification-table');
                    }
                    // 关闭弹窗
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                });
            } else {
                layer.msg(res.msg || '上传失败', {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.close(loadIndex);
            layer.msg('请求失败：' + error, {icon: 2});
        });

        return false;
    });
});

// 删除图片
function removeDocument() {
    $('#document_path').val('');
    $('#document_name').val('');
    $('#document-list').hide().html('');

    // 恢复上传区域状态
    var uploadArea = $('#upload-area');
    uploadArea.removeClass('has-file');
    uploadArea.find('.upload-text').text('点击选择图片或拖拽图片到此处');
    uploadArea.find('.upload-icon i').removeClass('layui-icon-ok').addClass('layui-icon-upload-drag');

    layer.msg('图片已删除', {icon: 1});
}

// 显示示例图片
function showExampleImage(imagePath, qualificationName) {
    if (!imagePath) {
        layui.layer.msg('暂无示例图片', { icon: 2 });
        return;
    }

    // 使用layer.open显示图片，控制大小
    layui.layer.open({
        type: 1,
        title: qualificationName + ' - 示例图片',
        area: ['600px', '450px'],
        maxmin: true,
        shadeClose: true,
        content: '<div style="text-align: center; padding: 20px;"><img src="' + imagePath + '" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="示例图片"></div>'
    });
}
</script>

</body>
</html>
