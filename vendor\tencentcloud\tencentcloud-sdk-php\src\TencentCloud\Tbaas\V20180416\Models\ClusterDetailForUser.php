<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tbaas\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 网络详情信息
 *
 * @method string getClusterId() 获取网络ID
 * @method void setClusterId(string $ClusterId) 设置网络ID
 * @method array getGroupList() 获取组织列表
 * @method void setGroupList(array $GroupList) 设置组织列表
 * @method string getClusterName() 获取网络名称
 * @method void setClusterName(string $ClusterName) 设置网络名称
 */
class ClusterDetailForUser extends AbstractModel
{
    /**
     * @var string 网络ID
     */
    public $ClusterId;

    /**
     * @var array 组织列表
     */
    public $GroupList;

    /**
     * @var string 网络名称
     */
    public $ClusterName;

    /**
     * @param string $ClusterId 网络ID
     * @param array $GroupList 组织列表
     * @param string $ClusterName 网络名称
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ClusterId",$param) and $param["ClusterId"] !== null) {
            $this->ClusterId = $param["ClusterId"];
        }

        if (array_key_exists("GroupList",$param) and $param["GroupList"] !== null) {
            $this->GroupList = [];
            foreach ($param["GroupList"] as $key => $value){
                $obj = new GroupDetailForUser();
                $obj->deserialize($value);
                array_push($this->GroupList, $obj);
            }
        }

        if (array_key_exists("ClusterName",$param) and $param["ClusterName"] !== null) {
            $this->ClusterName = $param["ClusterName"];
        }
    }
}
