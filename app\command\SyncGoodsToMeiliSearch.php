<?php

declare(strict_types=1);

namespace app\command;

use app\common\library\MeiliSearch;
use app\common\model\goods\Goods;
use think\console\Command;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\facade\Db;

/**
 * 同步商品数据到MeiliSearch
 */
class SyncGoodsToMeiliSearch extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('sync_goods_to_meilisearch')
            ->addOption('batch', 'b', Option::VALUE_OPTIONAL, '每批处理的数据量', 1000)
            ->addOption('reset', 'r', Option::VALUE_NONE, '重置索引')
            ->addOption('debug', 'd', Option::VALUE_NONE, '开启调试模式')
            ->setDescription('同步商品数据到MeiliSearch');
    }

    protected function execute(Input $input, Output $output)
    {
        $batchSize = (int)$input->getOption('batch');
        $reset = $input->getOption('reset');
        $debug = $input->getOption('debug');

        // 创建日志文件
        $logFile = runtime_path() . 'log/sync_goods_' . date('Y-m-d_H-i-s') . '.log';
        $this->log("开始同步商品数据到MeiliSearch...", $logFile);
        $output->writeln("开始同步商品数据到MeiliSearch...");
        $output->writeln("日志文件: {$logFile}");

        // 1. 详细的数据库连接测试
        if (!$this->testDatabaseConnection($output, $debug)) {
            $output->writeln("<error>数据库连接失败，终止同步</error>");
            return 1;
        }

        // 2. 详细的MeiliSearch连接测试
        $meili = new MeiliSearch();
        if (!$this->testMeiliSearchConnection($meili, $output, $debug)) {
            $output->writeln("<error>MeiliSearch连接失败，终止同步</error>");
            return 1;
        }

        // 3. 处理索引重置
        if ($reset) {
            $output->writeln("检测到重置选项，将重置索引");
            if (!$this->resetIndex($meili, $output, $debug)) {
                $output->writeln("<error>索引重置失败</error>");
                return 1;
            }
        }

        // 4. 创建或配置索引
        if (!$this->setupIndex($meili, $output, $debug)) {
            $output->writeln("<error>索引设置失败</error>");
            return Command::FAILURE;
        }

        // 5. 获取要同步的商品数量
        $total = $this->getGoodsCount($output, $debug);
        if ($total === 0) {
            $output->writeln("没有需要同步的商品");
            return Command::SUCCESS;
        }

        $output->writeln("共有 {$total} 个商品需要同步");

        // 6. 更新分词字段（如果分词功能可用）
        $this->updateGoodsSplitWords($output, $debug);

        // 7. 分批同步商品数据
        $result = $this->syncGoodsData($meili, $total, $batchSize, $output, $debug);

        // 8. 输出最终结果
        $this->log("同步完成！总计: {$result['total']} 个商品, 成功: {$result['success']} 个, 失败: {$result['error']} 个", $logFile);
        $output->writeln("同步完成！");
        $output->writeln("总计: {$result['total']} 个商品");
        $output->writeln("成功: {$result['success']} 个");
        $output->writeln("失败: {$result['error']} 个");

        if (!empty($result['errors'])) {
            $this->log("错误详情: " . json_encode($result['errors']), $logFile);
            if ($debug) {
                $output->writeln("错误详情:");
                foreach ($result['errors'] as $error) {
                    $output->writeln("  - " . json_encode($error));
                }
            }
        }

        // 更新定时任务的最后执行时间
        try {
            $commandName = 'sync_goods_to_meilisearch';
            $currentTime = time();
            Db::name('dev_crontab')
                ->where(['command' => $commandName])
                ->update([
                    'last_time' => $currentTime,
                    'update_time' => $currentTime
                ]);
            $output->writeln("已更新定时任务最后执行时间");
        } catch (\Exception $e) {
            $output->writeln("<e>更新定时任务最后执行时间失败: " . $e->getMessage() . "</e>");
        }

        return $result['error'] > 0 ? 1 : 0; // 返回0表示成功，1表示失败
    }

    /**
     * 记录日志到文件
     */
    protected function log(string $message, string $logFile): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;

        // 确保日志目录存在
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    /**
     * 测试数据库连接
     */
    protected function testDatabaseConnection(Output $output, bool $debug = false, string $logFile = ''): bool
    {
        try {
            $output->writeln("正在测试数据库连接...");

            // 测试基本连接
            $testResult = Db::query('SELECT 1 as test');
            if ($debug) {
                $output->writeln("数据库连接测试结果: " . json_encode($testResult));
            }

            // 测试关键表是否存在
            $tables = ['ls_goods', 'ls_goods_category', 'ls_goods_brand', 'ls_goods_column'];
            foreach ($tables as $table) {
                $exists = Db::query("SHOW TABLES LIKE '{$table}'");
                if (empty($exists)) {
                    $output->writeln("<error>表 {$table} 不存在</error>");
                    return false;
                }
            }

            $output->writeln("数据库连接测试成功");
            return true;
        } catch (\Exception $e) {
            $output->writeln("<error>数据库连接测试失败: " . $e->getMessage() . "</error>");
            if ($debug) {
                $output->writeln("错误堆栈: " . $e->getTraceAsString());
            }
            return false;
        }
    }

    /**
     * 测试MeiliSearch连接
     */
    protected function testMeiliSearchConnection(MeiliSearch $meili, Output $output, bool $debug = false): bool
    {
        try {
            $output->writeln("正在测试MeiliSearch连接...");

            // 简单测试连接，尝试执行一个简单操作
            try {
                // 尝试执行一个简单操作，例如创建一个临时索引
                $tempIndexName = 'test_connection_' . time();
                $meili->createIndex($tempIndexName);

                // 如果成功创建，尝试删除它
                try {
                    $meili->deleteIndex($tempIndexName);
                } catch (\Exception $e) {
                    if ($debug) {
                        $output->writeln("删除临时索引失败，但连接测试已成功: " . $e->getMessage());
                    }
                }

                if ($debug) {
                    $output->writeln("MeiliSearch连接测试成功：创建了临时索引");
                }
            } catch (\Exception $e) {
                // 如果创建索引失败，可能是因为权限问题，但连接可能是正常的
                if ($debug) {
                    $output->writeln("无法创建测试索引，但将继续尝试: " . $e->getMessage());
                }
            }

            $output->writeln("MeiliSearch连接测试成功");
            return true;
        } catch (\Exception $e) {
            $output->writeln("<error>MeiliSearch连接测试失败: " . $e->getMessage() . "</error>");
            if ($debug) {
                $output->writeln("错误堆栈: " . $e->getTraceAsString());
            }
            return false;
        }
    }

    /**
     * 重置索引
     */
    protected function resetIndex(MeiliSearch $meili, Output $output, bool $debug = false): bool
    {
        try {
            $output->writeln('正在重置索引...');
            $result = $meili->deleteIndex('goods');
            if ($debug) {
                $output->writeln("删除索引结果: " . json_encode($result));
            }

            // 等待删除完成
            sleep(2);
            $output->writeln('索引已重置');
            return true;
        } catch (\Exception $e) {
            $output->writeln("<error>重置索引失败: " . $e->getMessage() . "</error>");
            return false;
        }
    }

    /**
     * 设置索引
     */
    protected function setupIndex(MeiliSearch $meili, Output $output, bool $debug = false): bool
    {
        try {
            // 检查索引是否存在
            $indexExists = false;
            try {
                // 尝试获取索引信息，如果不存在会抛出异常
                $meili->getIndex('goods');
                $indexExists = true;
            } catch (\Exception $e) {
                // 索引不存在，需要创建
                $indexExists = false;
            }

            if (!$indexExists) {
                $output->writeln('正在创建索引...');

                $settings = [
                    'searchableAttributes' => [
                        'name',
                        'remark',
                        'content',
                        'split_word',
                        'category_path',
                        'brand_name',
                        'tags'
                    ],
                    'filterableAttributes' => [
                        'shop_id',
                        'first_cate_id',
                        'second_cate_id',
                        'third_cate_id',
                        'brand_id',
                        'is_hot',
                        'is_recommend',
                        'status',
                        'audit_status',
                        'del'
                    ],
                    'sortableAttributes' => [
                        'min_price',
                        'sales_actual',
                        'sales_virtual',
                        'create_time',
                        'update_time'
                    ],
                    'rankingRules' => [
                        'words',
                        'typo',
                        'proximity',
                        'attribute',
                        'sort',
                        'exactness'
                    ]
                ];

                $result = $meili->createIndex('goods', $settings);
                if ($debug) {
                    $output->writeln("创建索引结果: " . json_encode($result));
                }

                // 等待索引创建完成
                sleep(3);
                $output->writeln('索引已创建');
            } else {
                $output->writeln('索引已存在，继续同步数据');
            }

            return true;
        } catch (\Exception $e) {
            $output->writeln("<error>设置索引失败: " . $e->getMessage() . "</error>");
            return false;
        }
    }

    /**
     * 获取商品数量
     */
    protected function getGoodsCount(Output $output, bool $debug = false): int
    {
        try {
            $count = Goods::where([
                ['del', '=', 0],
                ['status', '=', 1],
                ['audit_status', '=', 1]
            ])->count();

            if ($debug) {
                $output->writeln("查询到的商品数量: {$count}");
            }

            return $count;
        } catch (\Exception $e) {
            $output->writeln("<error>获取商品数量失败: " . $e->getMessage() . "</error>");
            return 0;
        }
    }

    /**
     * 同步商品数据
     */
    protected function syncGoodsData(MeiliSearch $meili, int $total, int $batchSize, Output $output, bool $debug = false): array
    {
        $result = [
            'total' => $total,
            'processed' => 0,
            'success' => 0,
            'error' => 0,
            'errors' => []
        ];

        // 分批处理
        for ($offset = 0; $offset < $total; $offset += $batchSize) {
            try {
                $goodsBatch = $this->getGoodsBatch($offset, $batchSize, $debug);

                if (empty($goodsBatch)) {
                    $output->writeln("第 {$offset} 批数据为空，跳过");
                    break;
                }

                $documents = $this->prepareDocuments($goodsBatch, $output, $debug);
                $result['processed'] += count($documents);

                $output->writeln("正在处理第 " . ($offset + 1) . " 到 " . ($offset + count($documents)) . " 个商品");

                // 导入数据到MeiliSearch
                $response = $meili->importDocuments('goods', $documents, 'id');

                if (isset($response['taskUid'])) {
                    $result['success'] += count($documents);
                    $output->writeln("成功导入 " . count($documents) . " 个商品，任务ID：{$response['taskUid']}");
                } else {
                    $result['error'] += count($documents);
                    $result['errors'][] = $response;
                    $output->writeln("导入失败：" . json_encode($response));
                }

                // 短暂休息，避免过于频繁的请求
                usleep(100000); // 0.1秒

            } catch (\Exception $e) {
                $result['error'] += $batchSize;
                $result['errors'][] = $e->getMessage();
                $output->writeln("<error>处理第 {$offset} 批数据时出错: " . $e->getMessage() . "</error>");
            }
        }

        return $result;
    }

    /**
     * 获取商品批次数据
     */
    protected function getGoodsBatch(int $offset, int $batchSize, bool $debug = false): array
    {
        return Goods::where([
            ['del', '=', 0],
            ['status', '=', 1],
            ['audit_status', '=', 1]
        ])
            ->field('id, name, image, remark, del, status,join_jc,audit_status, content, shop_id, first_cate_id, second_cate_id, third_cate_id, brand_id, goods_label, goods_label_top, min_price, market_price, sales_actual, sales_virtual, is_hot, is_recommend, create_time, update_time')
            ->limit($offset, $batchSize) // 使用 limit 的两个参数形式，第一个是偏移量，第二个是数量
            ->select()
            ->toArray();
    }

    /**
     * 准备文档数据
     */
    protected function prepareDocuments(array $goodsBatch, Output $output, bool $debug = false): array
    {
        $documents = [];

        foreach ($goodsBatch as $goodsItem) {
            try {
                // 1. 获取分类路径
                $categoryPath = $this->getCategoryPath($goodsItem, $debug);

                // 2. 获取品牌名称
                $brandName = $this->getBrandName($goodsItem['brand_id'], $debug);

                // 3. 获取标签
                $tags = $this->getTags($goodsItem, $debug);

                // 准备文档
                $document = [
                    'id' => $goodsItem['id'],
                    'name' => $goodsItem['name'] ?? '',
                    'image' => $goodsItem['image'] ?? '',
                    'remark' => $goodsItem['remark'] ?? '',
                    'content' => $goodsItem['content'] ?? '',
                    'shop_id' => $goodsItem['shop_id'] ?? 0,
                    'first_cate_id' => $goodsItem['first_cate_id'] ?? 0,
                    'second_cate_id' => $goodsItem['second_cate_id'] ?? 0,
                    'third_cate_id' => $goodsItem['third_cate_id'] ?? 0,
                    'brand_id' => $goodsItem['brand_id'] ?? 0,
                    'min_price' => floatval($goodsItem['min_price'] ?? 0),
                    'market_price' => floatval($goodsItem['market_price'] ?? 0),
                    'sales_actual' => intval($goodsItem['sales_actual'] ?? 0),
                    'sales_virtual' => intval($goodsItem['sales_virtual'] ?? 0),
                    'is_hot' => intval($goodsItem['is_hot'] ?? 0),
                    'del' => intval($goodsItem['del'] ?? 0),
                    'join_jc' => intval($goodsItem['join_jc'] ?? 0),
                    'audit_status' => intval($goodsItem['audit_status'] ?? 0),
                    'status' => intval($goodsItem['status'] ?? 0),
                    'is_recommend' => intval($goodsItem['is_recommend'] ?? 0),
                    'create_time' => $goodsItem['create_time'] ?? 0,
                    'update_time' => $goodsItem['update_time'] ?? 0,
                    // 扩展字段
                    'category_path' => $categoryPath,
                    'brand_name' => $brandName,
                    'tags' => $tags,
                ];

                $documents[] = $document;
            } catch (\Exception $e) {
                $output->writeln("<error>准备商品ID {$goodsItem['id']} 的文档时出错: " . $e->getMessage() . "</error>");
            }
        }

        return $documents;
    }

    /**
     * 获取分类路径
     */
    protected function getCategoryPath(array $goodsItem, bool $debug = false): array
    {
        try {
            $categoryIds = array_filter([
                $goodsItem['first_cate_id'],
                $goodsItem['second_cate_id'],
                $goodsItem['third_cate_id']
            ]);

            if (empty($categoryIds)) {
                return [];
            }
            $categories = Db::name('goods_category')
                ->where(['id'=>$categoryIds])
                ->column('name');

            return array_values(array_filter($categories));
        } catch (\Exception $e) {
            if ($debug) {
                echo "获取分类路径失败: " . $e->getMessage() . "\n";
            }
            return [];
        }
    }

    /**
     * 获取品牌名称
     */
    protected function getBrandName(int $brandId, bool $debug = false): string
    {
        try {
            if (!$brandId) {
                return '';
            }

            $brandName = Db::name('goods_brand')
                ->where('id', $brandId)
                ->value('name');

            return $brandName ?: '';
        } catch (\Exception $e) {
            if ($debug) {
                echo "获取品牌名称失败: " . $e->getMessage() . "\n";
            }
            return '';
        }
    }

    /**
     * 获取标签
     */
    protected function getTags(array $goodsItem, bool $debug = false): array
    {
        try {
            $labelIds = [];

            if (!empty($goodsItem['goods_label'])) {
                $labelIds = array_merge($labelIds, explode(',', $goodsItem['goods_label']));
            }

            if (!empty($goodsItem['goods_label_top'])) {
                $labelIds = array_merge($labelIds, explode(',', $goodsItem['goods_label_top']));
            }

            $labelIds =array_filter($labelIds);

            if (empty($labelIds)) {
                return [];
            }

            $tags = Db::name('goods_column')
                ->whereIn('id', $labelIds)
                ->column('name');

            return array_values(array_filter($tags));
        } catch (\Exception $e) {
            if ($debug) {
                echo "获取标签失败: " . $e->getMessage() . "\n";
            }
            return [];
        }
    }

    /**
     * 更新商品分词字段
     */
    protected function updateGoodsSplitWords(Output $output, bool $debug = false)
    {
        try {
            // 检查分词函数是否可用
            if (!function_exists('Alisegment')) {
                $output->writeln("分词函数 Alisegment 不可用，跳过分词更新");
                return;
            }

            $output->writeln("正在更新商品分词字段...");

            // 获取需要更新分词的商品
            $goods = Db::name('goods')
                ->where([
                    ['del', '=', 0],
                    ['status', '=', 1],
                    ['audit_status', '=', 1],
                    ['split_word', '=', '']
                ])
                ->field('id, name, remark')
                ->limit(1000) // 限制数量，避免一次处理过多
                ->select()
                ->toArray();

            $total = count($goods);
            if ($total === 0) {
                $output->writeln("没有需要更新分词的商品");
                return;
            }

            $output->writeln("发现 {$total} 个商品需要更新分词字段");

            $processed = 0;
            $success = 0;
            $error = 0;

            foreach ($goods as $item) {
                $processed++;

                try {
                    $text = trim($item['name'] . ' ' . $item['remark']);
                    if (empty($text)) {
                        continue;
                    }

                    // 使用阿里云分词
                    $words = Alisegment($text);
                    $segmentedWords = !empty($words['words']) && is_array($words['words']) ? $words['words'] : [];

                    // 过滤分词结果
                    $validWords = [];
                    foreach ($segmentedWords as $word) {
                        $word = trim($word);
                        if (mb_strlen($word, 'UTF-8') >= 2) {
                            $validWords[] = $word;
                        }
                    }

                    // 去重并合并为字符串
                    $splitWord = implode(',', array_unique($validWords));

                    // 更新商品分词字段
                    Db::name('goods')
                        ->where('id', $item['id'])
                        ->update(['split_word' => $splitWord]);

                    $success++;

                    if ($processed % 50 === 0 || $processed === $total) {
                        $output->writeln("已处理 {$processed}/{$total} 个商品");
                    }
                } catch (\Exception $e) {
                    $error++;
                    if ($debug) {
                        $output->writeln("处理商品ID {$item['id']} 时出错: " . $e->getMessage());
                    }
                }
            }

            $output->writeln("分词字段更新完成，总共 {$total} 个商品，成功 {$success} 个，失败 {$error} 个");
        } catch (\Exception $e) {
            $output->writeln("<error>更新分词字段时出错: " . $e->getMessage() . "</error>");
        }
    }
}
