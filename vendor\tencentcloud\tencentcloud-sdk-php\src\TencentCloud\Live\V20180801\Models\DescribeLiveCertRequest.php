<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Live\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeLiveCert请求参数结构体
 *
 * @method integer getCertId() 获取DescribeLiveCerts接口获取到的证书Id。
 * @method void setCertId(integer $CertId) 设置DescribeLiveCerts接口获取到的证书Id。
 */
class DescribeLiveCertRequest extends AbstractModel
{
    /**
     * @var integer DescribeLiveCerts接口获取到的证书Id。
     */
    public $CertId;

    /**
     * @param integer $CertId DescribeLiveCerts接口获取到的证书Id。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("CertId",$param) and $param["CertId"] !== null) {
            $this->CertId = $param["CertId"];
        }
    }
}
