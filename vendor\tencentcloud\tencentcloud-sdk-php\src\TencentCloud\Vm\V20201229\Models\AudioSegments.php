<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Vm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 声音段信息
 *
 * @method string getOffsetTime() 获取截帧时间。
点播文件：该值为相对于视频偏移时间，单位为秒，例如：0，5，10
直播流：该值为时间戳，例如：**********
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setOffsetTime(string $OffsetTime) 设置截帧时间。
点播文件：该值为相对于视频偏移时间，单位为秒，例如：0，5，10
直播流：该值为时间戳，例如：**********
注意：此字段可能返回 null，表示取不到有效值。
 * @method AudioResult getResult() 获取结果集
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setResult(AudioResult $Result) 设置结果集
注意：此字段可能返回 null，表示取不到有效值。
 */
class AudioSegments extends AbstractModel
{
    /**
     * @var string 截帧时间。
点播文件：该值为相对于视频偏移时间，单位为秒，例如：0，5，10
直播流：该值为时间戳，例如：**********
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $OffsetTime;

    /**
     * @var AudioResult 结果集
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Result;

    /**
     * @param string $OffsetTime 截帧时间。
点播文件：该值为相对于视频偏移时间，单位为秒，例如：0，5，10
直播流：该值为时间戳，例如：**********
注意：此字段可能返回 null，表示取不到有效值。
     * @param AudioResult $Result 结果集
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("OffsetTime",$param) and $param["OffsetTime"] !== null) {
            $this->OffsetTime = $param["OffsetTime"];
        }

        if (array_key_exists("Result",$param) and $param["Result"] !== null) {
            $this->Result = new AudioResult();
            $this->Result->deserialize($param["Result"]);
        }
    }
}
