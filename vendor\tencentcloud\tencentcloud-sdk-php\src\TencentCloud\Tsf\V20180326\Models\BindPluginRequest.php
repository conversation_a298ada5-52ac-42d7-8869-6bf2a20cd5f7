<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tsf\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * BindPlugin请求参数结构体
 *
 * @method array getPluginInstanceList() 获取分组/API绑定插件列表
 * @method void setPluginInstanceList(array $PluginInstanceList) 设置分组/API绑定插件列表
 */
class BindPluginRequest extends AbstractModel
{
    /**
     * @var array 分组/API绑定插件列表
     */
    public $PluginInstanceList;

    /**
     * @param array $PluginInstanceList 分组/API绑定插件列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("PluginInstanceList",$param) and $param["PluginInstanceList"] !== null) {
            $this->PluginInstanceList = [];
            foreach ($param["PluginInstanceList"] as $key => $value){
                $obj = new GatewayPluginBoundParam();
                $obj->deserialize($value);
                array_push($this->PluginInstanceList, $obj);
            }
        }
    }
}
