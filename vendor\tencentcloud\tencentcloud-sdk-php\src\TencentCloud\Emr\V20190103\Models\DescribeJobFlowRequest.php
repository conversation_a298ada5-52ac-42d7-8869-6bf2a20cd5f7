<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Emr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeJobFlow请求参数结构体
 *
 * @method integer getJobFlowId() 获取流程任务Id，RunJobFlow接口返回的值。
 * @method void setJobFlowId(integer $JobFlowId) 设置流程任务Id，RunJobFlow接口返回的值。
 */
class DescribeJobFlowRequest extends AbstractModel
{
    /**
     * @var integer 流程任务Id，RunJobFlow接口返回的值。
     */
    public $JobFlowId;

    /**
     * @param integer $JobFlowId 流程任务Id，RunJobFlow接口返回的值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("JobFlowId",$param) and $param["JobFlowId"] !== null) {
            $this->JobFlowId = $param["JobFlowId"];
        }
    }
}
