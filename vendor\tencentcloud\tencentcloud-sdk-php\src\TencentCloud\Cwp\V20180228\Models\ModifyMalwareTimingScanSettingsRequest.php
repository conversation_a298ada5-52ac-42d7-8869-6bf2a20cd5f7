<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cwp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ModifyMalwareTimingScanSettings请求参数结构体
 *
 * @method integer getCheckPattern() 获取检测模式 0 全盘检测  1快速检测
 * @method void setCheckPattern(integer $CheckPattern) 设置检测模式 0 全盘检测  1快速检测
 * @method string getStartTime() 获取检测周期 开始时间，如：02:00:00
 * @method void setStartTime(string $StartTime) 设置检测周期 开始时间，如：02:00:00
 * @method string getEndTime() 获取检测周期 超时结束时间，如：04:00:00
 * @method void setEndTime(string $EndTime) 设置检测周期 超时结束时间，如：04:00:00
 * @method integer getIsGlobal() 获取是否全部服务器 1 全部 2 自选
 * @method void setIsGlobal(integer $IsGlobal) 设置是否全部服务器 1 全部 2 自选
 * @method integer getEnableScan() 获取定时检测开关 0 关闭 1开启
 * @method void setEnableScan(integer $EnableScan) 设置定时检测开关 0 关闭 1开启
 * @method integer getMonitoringPattern() 获取监控模式 0 标准 1深度
 * @method void setMonitoringPattern(integer $MonitoringPattern) 设置监控模式 0 标准 1深度
 * @method integer getCycle() 获取扫描周期 默认每天 1
 * @method void setCycle(integer $Cycle) 设置扫描周期 默认每天 1
 * @method integer getRealTimeMonitoring() 获取实时监控 0 关闭 1开启
 * @method void setRealTimeMonitoring(integer $RealTimeMonitoring) 设置实时监控 0 关闭 1开启
 * @method array getQuuidList() 获取自选服务器时必须 主机quuid的string数组
 * @method void setQuuidList(array $QuuidList) 设置自选服务器时必须 主机quuid的string数组
 */
class ModifyMalwareTimingScanSettingsRequest extends AbstractModel
{
    /**
     * @var integer 检测模式 0 全盘检测  1快速检测
     */
    public $CheckPattern;

    /**
     * @var string 检测周期 开始时间，如：02:00:00
     */
    public $StartTime;

    /**
     * @var string 检测周期 超时结束时间，如：04:00:00
     */
    public $EndTime;

    /**
     * @var integer 是否全部服务器 1 全部 2 自选
     */
    public $IsGlobal;

    /**
     * @var integer 定时检测开关 0 关闭 1开启
     */
    public $EnableScan;

    /**
     * @var integer 监控模式 0 标准 1深度
     */
    public $MonitoringPattern;

    /**
     * @var integer 扫描周期 默认每天 1
     */
    public $Cycle;

    /**
     * @var integer 实时监控 0 关闭 1开启
     */
    public $RealTimeMonitoring;

    /**
     * @var array 自选服务器时必须 主机quuid的string数组
     */
    public $QuuidList;

    /**
     * @param integer $CheckPattern 检测模式 0 全盘检测  1快速检测
     * @param string $StartTime 检测周期 开始时间，如：02:00:00
     * @param string $EndTime 检测周期 超时结束时间，如：04:00:00
     * @param integer $IsGlobal 是否全部服务器 1 全部 2 自选
     * @param integer $EnableScan 定时检测开关 0 关闭 1开启
     * @param integer $MonitoringPattern 监控模式 0 标准 1深度
     * @param integer $Cycle 扫描周期 默认每天 1
     * @param integer $RealTimeMonitoring 实时监控 0 关闭 1开启
     * @param array $QuuidList 自选服务器时必须 主机quuid的string数组
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("CheckPattern",$param) and $param["CheckPattern"] !== null) {
            $this->CheckPattern = $param["CheckPattern"];
        }

        if (array_key_exists("StartTime",$param) and $param["StartTime"] !== null) {
            $this->StartTime = $param["StartTime"];
        }

        if (array_key_exists("EndTime",$param) and $param["EndTime"] !== null) {
            $this->EndTime = $param["EndTime"];
        }

        if (array_key_exists("IsGlobal",$param) and $param["IsGlobal"] !== null) {
            $this->IsGlobal = $param["IsGlobal"];
        }

        if (array_key_exists("EnableScan",$param) and $param["EnableScan"] !== null) {
            $this->EnableScan = $param["EnableScan"];
        }

        if (array_key_exists("MonitoringPattern",$param) and $param["MonitoringPattern"] !== null) {
            $this->MonitoringPattern = $param["MonitoringPattern"];
        }

        if (array_key_exists("Cycle",$param) and $param["Cycle"] !== null) {
            $this->Cycle = $param["Cycle"];
        }

        if (array_key_exists("RealTimeMonitoring",$param) and $param["RealTimeMonitoring"] !== null) {
            $this->RealTimeMonitoring = $param["RealTimeMonitoring"];
        }

        if (array_key_exists("QuuidList",$param) and $param["QuuidList"] !== null) {
            $this->QuuidList = $param["QuuidList"];
        }
    }
}
