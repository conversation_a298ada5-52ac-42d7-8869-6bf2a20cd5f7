<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tbaas\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * GetChaincodeInitializeResultForUser返回参数结构体
 *
 * @method integer getInitResult() 获取实例化结果：0，实例化中；1，实例化成功；2，实例化失败
 * @method void setInitResult(integer $InitResult) 设置实例化结果：0，实例化中；1，实例化成功；2，实例化失败
 * @method string getInitMessage() 获取实例化信息
 * @method void setInitMessage(string $InitMessage) 设置实例化信息
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class GetChaincodeInitializeResultForUserResponse extends AbstractModel
{
    /**
     * @var integer 实例化结果：0，实例化中；1，实例化成功；2，实例化失败
     */
    public $InitResult;

    /**
     * @var string 实例化信息
     */
    public $InitMessage;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param integer $InitResult 实例化结果：0，实例化中；1，实例化成功；2，实例化失败
     * @param string $InitMessage 实例化信息
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InitResult",$param) and $param["InitResult"] !== null) {
            $this->InitResult = $param["InitResult"];
        }

        if (array_key_exists("InitMessage",$param) and $param["InitMessage"] !== null) {
            $this->InitMessage = $param["InitMessage"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
