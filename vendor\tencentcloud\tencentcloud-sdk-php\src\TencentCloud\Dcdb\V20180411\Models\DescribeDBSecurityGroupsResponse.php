<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dcdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeDBSecurityGroups返回参数结构体
 *
 * @method array getGroups() 获取安全组详情。
 * @method void setGroups(array $Groups) 设置安全组详情。
 * @method string getVIP() 获取实例VIP
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setVIP(string $VIP) 设置实例VIP
注意：此字段可能返回 null，表示取不到有效值。
 * @method integer getVPort() 获取实例端口
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setVPort(integer $VPort) 设置实例端口
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeDBSecurityGroupsResponse extends AbstractModel
{
    /**
     * @var array 安全组详情。
     */
    public $Groups;

    /**
     * @var string 实例VIP
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $VIP;

    /**
     * @var integer 实例端口
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $VPort;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $Groups 安全组详情。
     * @param string $VIP 实例VIP
注意：此字段可能返回 null，表示取不到有效值。
     * @param integer $VPort 实例端口
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Groups",$param) and $param["Groups"] !== null) {
            $this->Groups = [];
            foreach ($param["Groups"] as $key => $value){
                $obj = new SecurityGroup();
                $obj->deserialize($value);
                array_push($this->Groups, $obj);
            }
        }

        if (array_key_exists("VIP",$param) and $param["VIP"] !== null) {
            $this->VIP = $param["VIP"];
        }

        if (array_key_exists("VPort",$param) and $param["VPort"] !== null) {
            $this->VPort = $param["VPort"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
