{"name": "yansongda/supports", "description": "common components", "keywords": ["support", "array", "collection", "config", "http", "guzzle", "throttle"], "support": {"issues": "https://github.com/yansongda/supports/issues", "source": "https://github.com/yansongda/supports"}, "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.1.3", "monolog/monolog": "^1.23 || ^2.0", "guzzlehttp/guzzle": "^6.2 || ^7.0"}, "require-dev": {"predis/predis": "^1.1", "phpunit/phpunit": "^7.5", "friendsofphp/php-cs-fixer": "^2.15"}, "autoload": {"psr-4": {"Yansongda\\Supports\\": "src/"}}, "autoload-dev": {"psr-4": {"Yansongda\\Supports\\Tests\\": "tests/"}}, "suggest": {"predis/predis": "Allows to use throttle feature"}, "license": "MIT"}