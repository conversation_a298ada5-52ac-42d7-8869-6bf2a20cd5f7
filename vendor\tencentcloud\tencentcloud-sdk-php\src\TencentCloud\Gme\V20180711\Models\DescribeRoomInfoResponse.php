<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Gme\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeRoomInfo返回参数结构体
 *
 * @method integer getResult() 获取操作结果, 0成功, 非0失败
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setResult(integer $Result) 设置操作结果, 0成功, 非0失败
注意：此字段可能返回 null，表示取不到有效值。
 * @method array getRoomUsers() 获取房间用户信息
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setRoomUsers(array $RoomUsers) 设置房间用户信息
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeRoomInfoResponse extends AbstractModel
{
    /**
     * @var integer 操作结果, 0成功, 非0失败
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Result;

    /**
     * @var array 房间用户信息
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $RoomUsers;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param integer $Result 操作结果, 0成功, 非0失败
注意：此字段可能返回 null，表示取不到有效值。
     * @param array $RoomUsers 房间用户信息
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Result",$param) and $param["Result"] !== null) {
            $this->Result = $param["Result"];
        }

        if (array_key_exists("RoomUsers",$param) and $param["RoomUsers"] !== null) {
            $this->RoomUsers = [];
            foreach ($param["RoomUsers"] as $key => $value){
                $obj = new RoomUser();
                $obj->deserialize($value);
                array_push($this->RoomUsers, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
