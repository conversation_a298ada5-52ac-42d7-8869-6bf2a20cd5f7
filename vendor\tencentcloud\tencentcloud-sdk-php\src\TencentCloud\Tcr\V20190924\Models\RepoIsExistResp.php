<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tcr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 仓库是否存在的返回值
 *
 * @method boolean getIsExist() 获取仓库是否存在
 * @method void setIsExist(boolean $IsExist) 设置仓库是否存在
 */
class RepoIsExistResp extends AbstractModel
{
    /**
     * @var boolean 仓库是否存在
     */
    public $IsExist;

    /**
     * @param boolean $IsExist 仓库是否存在
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("IsExist",$param) and $param["IsExist"] !== null) {
            $this->IsExist = $param["IsExist"];
        }
    }
}
