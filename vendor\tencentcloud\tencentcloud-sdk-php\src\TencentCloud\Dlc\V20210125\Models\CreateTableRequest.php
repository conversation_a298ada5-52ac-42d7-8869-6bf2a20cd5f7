<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dlc\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreateTable请求参数结构体
 *
 * @method TableInfo getTableInfo() 获取数据表配置信息
 * @method void setTableInfo(TableInfo $TableInfo) 设置数据表配置信息
 */
class CreateTableRequest extends AbstractModel
{
    /**
     * @var TableInfo 数据表配置信息
     */
    public $TableInfo;

    /**
     * @param TableInfo $TableInfo 数据表配置信息
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("TableInfo",$param) and $param["TableInfo"] !== null) {
            $this->TableInfo = new TableInfo();
            $this->TableInfo->deserialize($param["TableInfo"]);
        }
    }
}
