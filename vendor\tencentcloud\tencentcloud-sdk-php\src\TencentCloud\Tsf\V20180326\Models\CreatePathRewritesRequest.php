<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tsf\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreatePathRewrites请求参数结构体
 *
 * @method PathRewriteCreateObject getPathRewrites() 获取路径重写列表
 * @method void setPathRewrites(PathRewriteCreateObject $PathRewrites) 设置路径重写列表
 */
class CreatePathRewritesRequest extends AbstractModel
{
    /**
     * @var PathRewriteCreateObject 路径重写列表
     */
    public $PathRewrites;

    /**
     * @param PathRewriteCreateObject $PathRewrites 路径重写列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("PathRewrites",$param) and $param["PathRewrites"] !== null) {
            $this->PathRewrites = new PathRewriteCreateObject();
            $this->PathRewrites->deserialize($param["PathRewrites"]);
        }
    }
}
