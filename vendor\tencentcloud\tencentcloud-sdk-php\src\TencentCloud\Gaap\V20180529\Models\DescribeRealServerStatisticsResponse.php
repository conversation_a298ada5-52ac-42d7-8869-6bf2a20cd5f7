<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Gaap\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeRealServerStatistics返回参数结构体
 *
 * @method array getStatisticsData() 获取指定监听器的源站状态统计数据
 * @method void setStatisticsData(array $StatisticsData) 设置指定监听器的源站状态统计数据
 * @method array getRsStatisticsData() 获取多个源站状态统计数据
 * @method void setRsStatisticsData(array $RsStatisticsData) 设置多个源站状态统计数据
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeRealServerStatisticsResponse extends AbstractModel
{
    /**
     * @var array 指定监听器的源站状态统计数据
     */
    public $StatisticsData;

    /**
     * @var array 多个源站状态统计数据
     */
    public $RsStatisticsData;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $StatisticsData 指定监听器的源站状态统计数据
     * @param array $RsStatisticsData 多个源站状态统计数据
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("StatisticsData",$param) and $param["StatisticsData"] !== null) {
            $this->StatisticsData = [];
            foreach ($param["StatisticsData"] as $key => $value){
                $obj = new StatisticsDataInfo();
                $obj->deserialize($value);
                array_push($this->StatisticsData, $obj);
            }
        }

        if (array_key_exists("RsStatisticsData",$param) and $param["RsStatisticsData"] !== null) {
            $this->RsStatisticsData = [];
            foreach ($param["RsStatisticsData"] as $key => $value){
                $obj = new MetricStatisticsInfo();
                $obj->deserialize($value);
                array_push($this->RsStatisticsData, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
