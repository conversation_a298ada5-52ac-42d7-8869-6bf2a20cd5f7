<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dayu\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * KeyValue记录
 *
 * @method array getRecord() 获取一条记录的Key-Value数组
 * @method void setRecord(array $Record) 设置一条记录的Key-Value数组
 */
class KeyValueRecord extends AbstractModel
{
    /**
     * @var array 一条记录的Key-Value数组
     */
    public $Record;

    /**
     * @param array $Record 一条记录的Key-Value数组
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Record",$param) and $param["Record"] !== null) {
            $this->Record = [];
            foreach ($param["Record"] as $key => $value){
                $obj = new KeyValue();
                $obj->deserialize($value);
                array_push($this->Record, $obj);
            }
        }
    }
}
