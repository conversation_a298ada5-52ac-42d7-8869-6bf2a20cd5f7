<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Live\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeLiveTranscodeRules请求参数结构体
 *
 * @method array getTemplateIds() 获取要筛选的模板ID数组。
 * @method void setTemplateIds(array $TemplateIds) 设置要筛选的模板ID数组。
 * @method array getDomainNames() 获取要筛选的域名数组。
 * @method void setDomainNames(array $DomainNames) 设置要筛选的域名数组。
 */
class DescribeLiveTranscodeRulesRequest extends AbstractModel
{
    /**
     * @var array 要筛选的模板ID数组。
     */
    public $TemplateIds;

    /**
     * @var array 要筛选的域名数组。
     */
    public $DomainNames;

    /**
     * @param array $TemplateIds 要筛选的模板ID数组。
     * @param array $DomainNames 要筛选的域名数组。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("TemplateIds",$param) and $param["TemplateIds"] !== null) {
            $this->TemplateIds = $param["TemplateIds"];
        }

        if (array_key_exists("DomainNames",$param) and $param["DomainNames"] !== null) {
            $this->DomainNames = $param["DomainNames"];
        }
    }
}
