<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Vpc\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ModifyHaVipAttribute请求参数结构体
 *
 * @method string getHaVipId() 获取`HAVIP`唯一`ID`，形如：`havip-9o233uri`。
 * @method void setHaVipId(string $HaVipId) 设置`HAVIP`唯一`ID`，形如：`havip-9o233uri`。
 * @method string getHaVipName() 获取`HAVIP`名称，可任意命名，但不得超过60个字符。
 * @method void setHaVipName(string $HaVipName) 设置`HAVIP`名称，可任意命名，但不得超过60个字符。
 */
class ModifyHaVipAttributeRequest extends AbstractModel
{
    /**
     * @var string `HAVIP`唯一`ID`，形如：`havip-9o233uri`。
     */
    public $HaVipId;

    /**
     * @var string `HAVIP`名称，可任意命名，但不得超过60个字符。
     */
    public $HaVipName;

    /**
     * @param string $HaVipId `HAVIP`唯一`ID`，形如：`havip-9o233uri`。
     * @param string $HaVipName `HAVIP`名称，可任意命名，但不得超过60个字符。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("HaVipId",$param) and $param["HaVipId"] !== null) {
            $this->HaVipId = $param["HaVipId"];
        }

        if (array_key_exists("HaVipName",$param) and $param["HaVipName"] !== null) {
            $this->HaVipName = $param["HaVipName"];
        }
    }
}
