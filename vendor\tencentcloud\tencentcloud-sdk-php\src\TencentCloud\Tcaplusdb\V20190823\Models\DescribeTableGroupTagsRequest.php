<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tcaplusdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeTableGroupTags请求参数结构体
 *
 * @method string getClusterId() 获取待查询标签表格组所属集群ID
 * @method void setClusterId(string $ClusterId) 设置待查询标签表格组所属集群ID
 * @method array getTableGroupIds() 获取待查询标签表格组ID列表
 * @method void setTableGroupIds(array $TableGroupIds) 设置待查询标签表格组ID列表
 */
class DescribeTableGroupTagsRequest extends AbstractModel
{
    /**
     * @var string 待查询标签表格组所属集群ID
     */
    public $ClusterId;

    /**
     * @var array 待查询标签表格组ID列表
     */
    public $TableGroupIds;

    /**
     * @param string $ClusterId 待查询标签表格组所属集群ID
     * @param array $TableGroupIds 待查询标签表格组ID列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ClusterId",$param) and $param["ClusterId"] !== null) {
            $this->ClusterId = $param["ClusterId"];
        }

        if (array_key_exists("TableGroupIds",$param) and $param["TableGroupIds"] !== null) {
            $this->TableGroupIds = $param["TableGroupIds"];
        }
    }
}
