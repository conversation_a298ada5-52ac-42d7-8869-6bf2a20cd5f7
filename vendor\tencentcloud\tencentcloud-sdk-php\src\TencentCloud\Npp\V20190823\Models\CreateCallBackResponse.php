<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Npp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreateCallBack返回参数结构体
 *
 * @method string getCallId() 获取话单id
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setCallId(string $CallId) 设置话单id
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getSrcDisplayNum() 获取主叫显示系统分配的固话号码
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setSrcDisplayNum(string $SrcDisplayNum) 设置主叫显示系统分配的固话号码
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getDstDisplayNum() 获取被叫显示系统分配的固话号码
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setDstDisplayNum(string $DstDisplayNum) 设置被叫显示系统分配的固话号码
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getErrorCode() 获取错误码
 * @method void setErrorCode(string $ErrorCode) 设置错误码
 * @method string getMsg() 获取错误原因
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setMsg(string $Msg) 设置错误原因
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class CreateCallBackResponse extends AbstractModel
{
    /**
     * @var string 话单id
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $CallId;

    /**
     * @var string 主叫显示系统分配的固话号码
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $SrcDisplayNum;

    /**
     * @var string 被叫显示系统分配的固话号码
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $DstDisplayNum;

    /**
     * @var string 错误码
     */
    public $ErrorCode;

    /**
     * @var string 错误原因
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Msg;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param string $CallId 话单id
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $SrcDisplayNum 主叫显示系统分配的固话号码
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $DstDisplayNum 被叫显示系统分配的固话号码
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $ErrorCode 错误码
     * @param string $Msg 错误原因
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("CallId",$param) and $param["CallId"] !== null) {
            $this->CallId = $param["CallId"];
        }

        if (array_key_exists("SrcDisplayNum",$param) and $param["SrcDisplayNum"] !== null) {
            $this->SrcDisplayNum = $param["SrcDisplayNum"];
        }

        if (array_key_exists("DstDisplayNum",$param) and $param["DstDisplayNum"] !== null) {
            $this->DstDisplayNum = $param["DstDisplayNum"];
        }

        if (array_key_exists("ErrorCode",$param) and $param["ErrorCode"] !== null) {
            $this->ErrorCode = $param["ErrorCode"];
        }

        if (array_key_exists("Msg",$param) and $param["Msg"] !== null) {
            $this->Msg = $param["Msg"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
