<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cwp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 恶意文件详情
 *
 * @method string getVirusName() 获取病毒名称
 * @method void setVirusName(string $VirusName) 设置病毒名称
 * @method integer getFileSize() 获取文件大小
 * @method void setFileSize(integer $FileSize) 设置文件大小
 * @method string getMD5() 获取文件MD5
 * @method void setMD5(string $MD5) 设置文件MD5
 * @method string getFilePath() 获取文件地址
 * @method void setFilePath(string $FilePath) 设置文件地址
 * @method string getFileCreateTime() 获取首次运行时间
 * @method void setFileCreateTime(string $FileCreateTime) 设置首次运行时间
 * @method string getFileModifierTime() 获取最近一次运行时间
 * @method void setFileModifierTime(string $FileModifierTime) 设置最近一次运行时间
 * @method string getHarmDescribe() 获取危害描述
 * @method void setHarmDescribe(string $HarmDescribe) 设置危害描述
 * @method string getSuggestScheme() 获取建议方案
 * @method void setSuggestScheme(string $SuggestScheme) 设置建议方案
 * @method string getServersName() 获取服务器名称
 * @method void setServersName(string $ServersName) 设置服务器名称
 * @method string getHostIp() 获取服务器IP
 * @method void setHostIp(string $HostIp) 设置服务器IP
 * @method string getProcessName() 获取进程名称
 * @method void setProcessName(string $ProcessName) 设置进程名称
 * @method string getProcessID() 获取进程ID
 * @method void setProcessID(string $ProcessID) 设置进程ID
 * @method array getTags() 获取标签特性
 * @method void setTags(array $Tags) 设置标签特性
 * @method string getBreadth() 获取影响广度 // 暂时不提供
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setBreadth(string $Breadth) 设置影响广度 // 暂时不提供
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getHeat() 获取查询热度 // 暂时不提供
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setHeat(string $Heat) 设置查询热度 // 暂时不提供
注意：此字段可能返回 null，表示取不到有效值。
 * @method integer getId() 获取唯一ID
 * @method void setId(integer $Id) 设置唯一ID
 * @method string getFileName() 获取文件名称
 * @method void setFileName(string $FileName) 设置文件名称
 * @method string getCreateTime() 获取首次发现时间
 * @method void setCreateTime(string $CreateTime) 设置首次发现时间
 * @method string getLatestScanTime() 获取最近扫描时间
 * @method void setLatestScanTime(string $LatestScanTime) 设置最近扫描时间
 */
class MalwareInfo extends AbstractModel
{
    /**
     * @var string 病毒名称
     */
    public $VirusName;

    /**
     * @var integer 文件大小
     */
    public $FileSize;

    /**
     * @var string 文件MD5
     */
    public $MD5;

    /**
     * @var string 文件地址
     */
    public $FilePath;

    /**
     * @var string 首次运行时间
     */
    public $FileCreateTime;

    /**
     * @var string 最近一次运行时间
     */
    public $FileModifierTime;

    /**
     * @var string 危害描述
     */
    public $HarmDescribe;

    /**
     * @var string 建议方案
     */
    public $SuggestScheme;

    /**
     * @var string 服务器名称
     */
    public $ServersName;

    /**
     * @var string 服务器IP
     */
    public $HostIp;

    /**
     * @var string 进程名称
     */
    public $ProcessName;

    /**
     * @var string 进程ID
     */
    public $ProcessID;

    /**
     * @var array 标签特性
     */
    public $Tags;

    /**
     * @var string 影响广度 // 暂时不提供
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Breadth;

    /**
     * @var string 查询热度 // 暂时不提供
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Heat;

    /**
     * @var integer 唯一ID
     */
    public $Id;

    /**
     * @var string 文件名称
     */
    public $FileName;

    /**
     * @var string 首次发现时间
     */
    public $CreateTime;

    /**
     * @var string 最近扫描时间
     */
    public $LatestScanTime;

    /**
     * @param string $VirusName 病毒名称
     * @param integer $FileSize 文件大小
     * @param string $MD5 文件MD5
     * @param string $FilePath 文件地址
     * @param string $FileCreateTime 首次运行时间
     * @param string $FileModifierTime 最近一次运行时间
     * @param string $HarmDescribe 危害描述
     * @param string $SuggestScheme 建议方案
     * @param string $ServersName 服务器名称
     * @param string $HostIp 服务器IP
     * @param string $ProcessName 进程名称
     * @param string $ProcessID 进程ID
     * @param array $Tags 标签特性
     * @param string $Breadth 影响广度 // 暂时不提供
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $Heat 查询热度 // 暂时不提供
注意：此字段可能返回 null，表示取不到有效值。
     * @param integer $Id 唯一ID
     * @param string $FileName 文件名称
     * @param string $CreateTime 首次发现时间
     * @param string $LatestScanTime 最近扫描时间
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("VirusName",$param) and $param["VirusName"] !== null) {
            $this->VirusName = $param["VirusName"];
        }

        if (array_key_exists("FileSize",$param) and $param["FileSize"] !== null) {
            $this->FileSize = $param["FileSize"];
        }

        if (array_key_exists("MD5",$param) and $param["MD5"] !== null) {
            $this->MD5 = $param["MD5"];
        }

        if (array_key_exists("FilePath",$param) and $param["FilePath"] !== null) {
            $this->FilePath = $param["FilePath"];
        }

        if (array_key_exists("FileCreateTime",$param) and $param["FileCreateTime"] !== null) {
            $this->FileCreateTime = $param["FileCreateTime"];
        }

        if (array_key_exists("FileModifierTime",$param) and $param["FileModifierTime"] !== null) {
            $this->FileModifierTime = $param["FileModifierTime"];
        }

        if (array_key_exists("HarmDescribe",$param) and $param["HarmDescribe"] !== null) {
            $this->HarmDescribe = $param["HarmDescribe"];
        }

        if (array_key_exists("SuggestScheme",$param) and $param["SuggestScheme"] !== null) {
            $this->SuggestScheme = $param["SuggestScheme"];
        }

        if (array_key_exists("ServersName",$param) and $param["ServersName"] !== null) {
            $this->ServersName = $param["ServersName"];
        }

        if (array_key_exists("HostIp",$param) and $param["HostIp"] !== null) {
            $this->HostIp = $param["HostIp"];
        }

        if (array_key_exists("ProcessName",$param) and $param["ProcessName"] !== null) {
            $this->ProcessName = $param["ProcessName"];
        }

        if (array_key_exists("ProcessID",$param) and $param["ProcessID"] !== null) {
            $this->ProcessID = $param["ProcessID"];
        }

        if (array_key_exists("Tags",$param) and $param["Tags"] !== null) {
            $this->Tags = $param["Tags"];
        }

        if (array_key_exists("Breadth",$param) and $param["Breadth"] !== null) {
            $this->Breadth = $param["Breadth"];
        }

        if (array_key_exists("Heat",$param) and $param["Heat"] !== null) {
            $this->Heat = $param["Heat"];
        }

        if (array_key_exists("Id",$param) and $param["Id"] !== null) {
            $this->Id = $param["Id"];
        }

        if (array_key_exists("FileName",$param) and $param["FileName"] !== null) {
            $this->FileName = $param["FileName"];
        }

        if (array_key_exists("CreateTime",$param) and $param["CreateTime"] !== null) {
            $this->CreateTime = $param["CreateTime"];
        }

        if (array_key_exists("LatestScanTime",$param) and $param["LatestScanTime"] !== null) {
            $this->LatestScanTime = $param["LatestScanTime"];
        }
    }
}
