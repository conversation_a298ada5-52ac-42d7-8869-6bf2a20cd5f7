<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cvm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeInstancesOperationLimit返回参数结构体
 *
 * @method array getInstanceOperationLimitSet() 获取该参数表示调整配置操作（降配）限制次数查询。
 * @method void setInstanceOperationLimitSet(array $InstanceOperationLimitSet) 设置该参数表示调整配置操作（降配）限制次数查询。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeInstancesOperationLimitResponse extends AbstractModel
{
    /**
     * @var array 该参数表示调整配置操作（降配）限制次数查询。
     */
    public $InstanceOperationLimitSet;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $InstanceOperationLimitSet 该参数表示调整配置操作（降配）限制次数查询。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InstanceOperationLimitSet",$param) and $param["InstanceOperationLimitSet"] !== null) {
            $this->InstanceOperationLimitSet = [];
            foreach ($param["InstanceOperationLimitSet"] as $key => $value){
                $obj = new OperationCountLimit();
                $obj->deserialize($value);
                array_push($this->InstanceOperationLimitSet, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
