<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tke\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeAvailableClusterVersion返回参数结构体
 *
 * @method array getVersions() 获取可升级的集群版本号
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setVersions(array $Versions) 设置可升级的集群版本号
注意：此字段可能返回 null，表示取不到有效值。
 * @method array getClusters() 获取集群信息
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setClusters(array $Clusters) 设置集群信息
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeAvailableClusterVersionResponse extends AbstractModel
{
    /**
     * @var array 可升级的集群版本号
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Versions;

    /**
     * @var array 集群信息
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Clusters;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $Versions 可升级的集群版本号
注意：此字段可能返回 null，表示取不到有效值。
     * @param array $Clusters 集群信息
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Versions",$param) and $param["Versions"] !== null) {
            $this->Versions = $param["Versions"];
        }

        if (array_key_exists("Clusters",$param) and $param["Clusters"] !== null) {
            $this->Clusters = [];
            foreach ($param["Clusters"] as $key => $value){
                $obj = new ClusterVersion();
                $obj->deserialize($value);
                array_push($this->Clusters, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
