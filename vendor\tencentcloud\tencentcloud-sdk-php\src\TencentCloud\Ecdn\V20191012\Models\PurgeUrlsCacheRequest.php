<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ecdn\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * PurgeUrlsCache请求参数结构体
 *
 * @method array getUrls() 获取要刷新的Url列表，必须包含协议头部。
 * @method void setUrls(array $Urls) 设置要刷新的Url列表，必须包含协议头部。
 */
class PurgeUrlsCacheRequest extends AbstractModel
{
    /**
     * @var array 要刷新的Url列表，必须包含协议头部。
     */
    public $Urls;

    /**
     * @param array $Urls 要刷新的Url列表，必须包含协议头部。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Urls",$param) and $param["Urls"] !== null) {
            $this->Urls = $param["Urls"];
        }
    }
}
