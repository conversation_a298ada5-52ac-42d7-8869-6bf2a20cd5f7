<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Sqlserver\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreatePublishSubscribe请求参数结构体
 *
 * @method string getPublishInstanceId() 获取发布实例ID，形如mssql-j8kv137v
 * @method void setPublishInstanceId(string $PublishInstanceId) 设置发布实例ID，形如mssql-j8kv137v
 * @method string getSubscribeInstanceId() 获取订阅实例ID，形如mssql-j8kv137v
 * @method void setSubscribeInstanceId(string $SubscribeInstanceId) 设置订阅实例ID，形如mssql-j8kv137v
 * @method array getDatabaseTupleSet() 获取数据库的订阅发布关系集合
 * @method void setDatabaseTupleSet(array $DatabaseTupleSet) 设置数据库的订阅发布关系集合
 * @method string getPublishSubscribeName() 获取发布订阅的名称，默认值为：default_name
 * @method void setPublishSubscribeName(string $PublishSubscribeName) 设置发布订阅的名称，默认值为：default_name
 */
class CreatePublishSubscribeRequest extends AbstractModel
{
    /**
     * @var string 发布实例ID，形如mssql-j8kv137v
     */
    public $PublishInstanceId;

    /**
     * @var string 订阅实例ID，形如mssql-j8kv137v
     */
    public $SubscribeInstanceId;

    /**
     * @var array 数据库的订阅发布关系集合
     */
    public $DatabaseTupleSet;

    /**
     * @var string 发布订阅的名称，默认值为：default_name
     */
    public $PublishSubscribeName;

    /**
     * @param string $PublishInstanceId 发布实例ID，形如mssql-j8kv137v
     * @param string $SubscribeInstanceId 订阅实例ID，形如mssql-j8kv137v
     * @param array $DatabaseTupleSet 数据库的订阅发布关系集合
     * @param string $PublishSubscribeName 发布订阅的名称，默认值为：default_name
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("PublishInstanceId",$param) and $param["PublishInstanceId"] !== null) {
            $this->PublishInstanceId = $param["PublishInstanceId"];
        }

        if (array_key_exists("SubscribeInstanceId",$param) and $param["SubscribeInstanceId"] !== null) {
            $this->SubscribeInstanceId = $param["SubscribeInstanceId"];
        }

        if (array_key_exists("DatabaseTupleSet",$param) and $param["DatabaseTupleSet"] !== null) {
            $this->DatabaseTupleSet = [];
            foreach ($param["DatabaseTupleSet"] as $key => $value){
                $obj = new DatabaseTuple();
                $obj->deserialize($value);
                array_push($this->DatabaseTupleSet, $obj);
            }
        }

        if (array_key_exists("PublishSubscribeName",$param) and $param["PublishSubscribeName"] !== null) {
            $this->PublishSubscribeName = $param["PublishSubscribeName"];
        }
    }
}
