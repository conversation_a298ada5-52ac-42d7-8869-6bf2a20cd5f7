<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tse\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ManageConfig请求参数结构体
 *
 * @method string getInstanceId() 获取实例ID
 * @method void setInstanceId(string $InstanceId) 设置实例ID
 * @method string getType() 获取配置中心类型（consul、zookeeper、apollo等）
 * @method void setType(string $Type) 设置配置中心类型（consul、zookeeper、apollo等）
 * @method string getCommand() 获取请求命名 PUT GET DELETE
 * @method void setCommand(string $Command) 设置请求命名 PUT GET DELETE
 * @method string getKey() 获取配置的Key
 * @method void setKey(string $Key) 设置配置的Key
 * @method string getValue() 获取配置的Value
 * @method void setValue(string $Value) 设置配置的Value
 */
class ManageConfigRequest extends AbstractModel
{
    /**
     * @var string 实例ID
     */
    public $InstanceId;

    /**
     * @var string 配置中心类型（consul、zookeeper、apollo等）
     */
    public $Type;

    /**
     * @var string 请求命名 PUT GET DELETE
     */
    public $Command;

    /**
     * @var string 配置的Key
     */
    public $Key;

    /**
     * @var string 配置的Value
     */
    public $Value;

    /**
     * @param string $InstanceId 实例ID
     * @param string $Type 配置中心类型（consul、zookeeper、apollo等）
     * @param string $Command 请求命名 PUT GET DELETE
     * @param string $Key 配置的Key
     * @param string $Value 配置的Value
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InstanceId",$param) and $param["InstanceId"] !== null) {
            $this->InstanceId = $param["InstanceId"];
        }

        if (array_key_exists("Type",$param) and $param["Type"] !== null) {
            $this->Type = $param["Type"];
        }

        if (array_key_exists("Command",$param) and $param["Command"] !== null) {
            $this->Command = $param["Command"];
        }

        if (array_key_exists("Key",$param) and $param["Key"] !== null) {
            $this->Key = $param["Key"];
        }

        if (array_key_exists("Value",$param) and $param["Value"] !== null) {
            $this->Value = $param["Value"];
        }
    }
}
