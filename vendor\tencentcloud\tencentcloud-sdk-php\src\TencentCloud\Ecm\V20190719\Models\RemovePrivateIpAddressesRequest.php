<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ecm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * RemovePrivateIpAddresses请求参数结构体
 *
 * @method string getEcmRegion() 获取ECM 地域，形如ap-xian-ecm。
 * @method void setEcmRegion(string $EcmRegion) 设置ECM 地域，形如ap-xian-ecm。
 * @method string getNetworkInterfaceId() 获取弹性网卡实例ID，例如：eni-11112222。
 * @method void setNetworkInterfaceId(string $NetworkInterfaceId) 设置弹性网卡实例ID，例如：eni-11112222。
 * @method array getPrivateIpAddresses() 获取指定的内网IP信息，单次最多指定10个。
 * @method void setPrivateIpAddresses(array $PrivateIpAddresses) 设置指定的内网IP信息，单次最多指定10个。
 */
class RemovePrivateIpAddressesRequest extends AbstractModel
{
    /**
     * @var string ECM 地域，形如ap-xian-ecm。
     */
    public $EcmRegion;

    /**
     * @var string 弹性网卡实例ID，例如：eni-11112222。
     */
    public $NetworkInterfaceId;

    /**
     * @var array 指定的内网IP信息，单次最多指定10个。
     */
    public $PrivateIpAddresses;

    /**
     * @param string $EcmRegion ECM 地域，形如ap-xian-ecm。
     * @param string $NetworkInterfaceId 弹性网卡实例ID，例如：eni-11112222。
     * @param array $PrivateIpAddresses 指定的内网IP信息，单次最多指定10个。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("EcmRegion",$param) and $param["EcmRegion"] !== null) {
            $this->EcmRegion = $param["EcmRegion"];
        }

        if (array_key_exists("NetworkInterfaceId",$param) and $param["NetworkInterfaceId"] !== null) {
            $this->NetworkInterfaceId = $param["NetworkInterfaceId"];
        }

        if (array_key_exists("PrivateIpAddresses",$param) and $param["PrivateIpAddresses"] !== null) {
            $this->PrivateIpAddresses = [];
            foreach ($param["PrivateIpAddresses"] as $key => $value){
                $obj = new PrivateIpAddressSpecification();
                $obj->deserialize($value);
                array_push($this->PrivateIpAddresses, $obj);
            }
        }
    }
}
