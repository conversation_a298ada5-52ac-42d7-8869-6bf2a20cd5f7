<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Emr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 登录设置
 *
 * @method string getPassword() 获取Password
 * @method void setPassword(string $Password) 设置Password
 * @method string getPublicKeyId() 获取Public Key
 * @method void setPublicKeyId(string $PublicKeyId) 设置Public Key
 */
class LoginSettings extends AbstractModel
{
    /**
     * @var string Password
     */
    public $Password;

    /**
     * @var string Public Key
     */
    public $PublicKeyId;

    /**
     * @param string $Password Password
     * @param string $PublicKeyId Public Key
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Password",$param) and $param["Password"] !== null) {
            $this->Password = $param["Password"];
        }

        if (array_key_exists("PublicKeyId",$param) and $param["PublicKeyId"] !== null) {
            $this->PublicKeyId = $param["PublicKeyId"];
        }
    }
}
