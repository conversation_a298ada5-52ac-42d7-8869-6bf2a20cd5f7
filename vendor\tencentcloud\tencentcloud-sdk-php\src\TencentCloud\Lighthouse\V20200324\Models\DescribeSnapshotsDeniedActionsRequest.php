<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Lighthouse\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeSnapshotsDeniedActions请求参数结构体
 *
 * @method array getSnapshotIds() 获取快照 ID 列表, 可通过 DescribeSnapshots 查询。
 * @method void setSnapshotIds(array $SnapshotIds) 设置快照 ID 列表, 可通过 DescribeSnapshots 查询。
 */
class DescribeSnapshotsDeniedActionsRequest extends AbstractModel
{
    /**
     * @var array 快照 ID 列表, 可通过 DescribeSnapshots 查询。
     */
    public $SnapshotIds;

    /**
     * @param array $SnapshotIds 快照 ID 列表, 可通过 DescribeSnapshots 查询。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SnapshotIds",$param) and $param["SnapshotIds"] !== null) {
            $this->SnapshotIds = $param["SnapshotIds"];
        }
    }
}
