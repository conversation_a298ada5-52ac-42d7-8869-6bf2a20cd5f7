<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dcdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ModifyDBInstancesProject请求参数结构体
 *
 * @method array getInstanceIds() 获取待修改的实例ID列表。实例 ID 形如：dcdbt-ow728lmc。
 * @method void setInstanceIds(array $InstanceIds) 设置待修改的实例ID列表。实例 ID 形如：dcdbt-ow728lmc。
 * @method integer getProjectId() 获取要分配的项目 ID，可以通过 DescribeProjects 查询项目列表接口获取。
 * @method void setProjectId(integer $ProjectId) 设置要分配的项目 ID，可以通过 DescribeProjects 查询项目列表接口获取。
 */
class ModifyDBInstancesProjectRequest extends AbstractModel
{
    /**
     * @var array 待修改的实例ID列表。实例 ID 形如：dcdbt-ow728lmc。
     */
    public $InstanceIds;

    /**
     * @var integer 要分配的项目 ID，可以通过 DescribeProjects 查询项目列表接口获取。
     */
    public $ProjectId;

    /**
     * @param array $InstanceIds 待修改的实例ID列表。实例 ID 形如：dcdbt-ow728lmc。
     * @param integer $ProjectId 要分配的项目 ID，可以通过 DescribeProjects 查询项目列表接口获取。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InstanceIds",$param) and $param["InstanceIds"] !== null) {
            $this->InstanceIds = $param["InstanceIds"];
        }

        if (array_key_exists("ProjectId",$param) and $param["ProjectId"] !== null) {
            $this->ProjectId = $param["ProjectId"];
        }
    }
}
