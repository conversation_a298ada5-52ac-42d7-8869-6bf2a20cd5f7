<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cvm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * InquirePricePurchaseReservedInstancesOffering请求参数结构体
 *
 * @method integer getInstanceCount() 获取购买预留实例计费数量
 * @method void setInstanceCount(integer $InstanceCount) 设置购买预留实例计费数量
 * @method string getReservedInstancesOfferingId() 获取预留实例计费配置ID
 * @method void setReservedInstancesOfferingId(string $ReservedInstancesOfferingId) 设置预留实例计费配置ID
 * @method boolean getDryRun() 获取试运行
 * @method void setDryRun(boolean $DryRun) 设置试运行
 * @method string getClientToken() 获取用于保证请求幂等性的字符串。该字符串由客户生成，需保证不同请求之间唯一，最大值不超过64个ASCII字符。若不指定该参数，则无法保证请求的幂等性。<br>更多详细信息请参阅：如何保证幂等性
 * @method void setClientToken(string $ClientToken) 设置用于保证请求幂等性的字符串。该字符串由客户生成，需保证不同请求之间唯一，最大值不超过64个ASCII字符。若不指定该参数，则无法保证请求的幂等性。<br>更多详细信息请参阅：如何保证幂等性
 * @method string getReservedInstanceName() 获取预留实例显示名称。<br><li>不指定实例显示名称则默认显示‘未命名’。</li><li>最多支持60个字符（包含模式串）。</li>
 * @method void setReservedInstanceName(string $ReservedInstanceName) 设置预留实例显示名称。<br><li>不指定实例显示名称则默认显示‘未命名’。</li><li>最多支持60个字符（包含模式串）。</li>
 */
class InquirePricePurchaseReservedInstancesOfferingRequest extends AbstractModel
{
    /**
     * @var integer 购买预留实例计费数量
     */
    public $InstanceCount;

    /**
     * @var string 预留实例计费配置ID
     */
    public $ReservedInstancesOfferingId;

    /**
     * @var boolean 试运行
     */
    public $DryRun;

    /**
     * @var string 用于保证请求幂等性的字符串。该字符串由客户生成，需保证不同请求之间唯一，最大值不超过64个ASCII字符。若不指定该参数，则无法保证请求的幂等性。<br>更多详细信息请参阅：如何保证幂等性
     */
    public $ClientToken;

    /**
     * @var string 预留实例显示名称。<br><li>不指定实例显示名称则默认显示‘未命名’。</li><li>最多支持60个字符（包含模式串）。</li>
     */
    public $ReservedInstanceName;

    /**
     * @param integer $InstanceCount 购买预留实例计费数量
     * @param string $ReservedInstancesOfferingId 预留实例计费配置ID
     * @param boolean $DryRun 试运行
     * @param string $ClientToken 用于保证请求幂等性的字符串。该字符串由客户生成，需保证不同请求之间唯一，最大值不超过64个ASCII字符。若不指定该参数，则无法保证请求的幂等性。<br>更多详细信息请参阅：如何保证幂等性
     * @param string $ReservedInstanceName 预留实例显示名称。<br><li>不指定实例显示名称则默认显示‘未命名’。</li><li>最多支持60个字符（包含模式串）。</li>
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InstanceCount",$param) and $param["InstanceCount"] !== null) {
            $this->InstanceCount = $param["InstanceCount"];
        }

        if (array_key_exists("ReservedInstancesOfferingId",$param) and $param["ReservedInstancesOfferingId"] !== null) {
            $this->ReservedInstancesOfferingId = $param["ReservedInstancesOfferingId"];
        }

        if (array_key_exists("DryRun",$param) and $param["DryRun"] !== null) {
            $this->DryRun = $param["DryRun"];
        }

        if (array_key_exists("ClientToken",$param) and $param["ClientToken"] !== null) {
            $this->ClientToken = $param["ClientToken"];
        }

        if (array_key_exists("ReservedInstanceName",$param) and $param["ReservedInstanceName"] !== null) {
            $this->ReservedInstanceName = $param["ReservedInstanceName"];
        }
    }
}
