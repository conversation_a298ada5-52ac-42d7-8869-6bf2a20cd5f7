<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ssa\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeComplianceList返回参数结构体
 *
 * @method array getData() 获取检查项列表
 * @method void setData(array $Data) 设置检查项列表
 * @method integer getAssetTotalNum() 获取总检查资产数
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setAssetTotalNum(integer $AssetTotalNum) 设置总检查资产数
注意：此字段可能返回 null，表示取不到有效值。
 * @method integer getConfigTotalNum() 获取总检查项
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setConfigTotalNum(integer $ConfigTotalNum) 设置总检查项
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeComplianceListResponse extends AbstractModel
{
    /**
     * @var array 检查项列表
     */
    public $Data;

    /**
     * @var integer 总检查资产数
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $AssetTotalNum;

    /**
     * @var integer 总检查项
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $ConfigTotalNum;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $Data 检查项列表
     * @param integer $AssetTotalNum 总检查资产数
注意：此字段可能返回 null，表示取不到有效值。
     * @param integer $ConfigTotalNum 总检查项
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Data",$param) and $param["Data"] !== null) {
            $this->Data = [];
            foreach ($param["Data"] as $key => $value){
                $obj = new DataCompliance();
                $obj->deserialize($value);
                array_push($this->Data, $obj);
            }
        }

        if (array_key_exists("AssetTotalNum",$param) and $param["AssetTotalNum"] !== null) {
            $this->AssetTotalNum = $param["AssetTotalNum"];
        }

        if (array_key_exists("ConfigTotalNum",$param) and $param["ConfigTotalNum"] !== null) {
            $this->ConfigTotalNum = $param["ConfigTotalNum"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
