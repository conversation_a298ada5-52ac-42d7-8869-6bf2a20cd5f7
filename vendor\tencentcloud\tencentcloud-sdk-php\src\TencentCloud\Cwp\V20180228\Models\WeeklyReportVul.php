<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cwp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 专业版周报漏洞数据。
 *
 * @method string getMachineIp() 获取主机内网IP。
 * @method void setMachineIp(string $MachineIp) 设置主机内网IP。
 * @method string getVulName() 获取漏洞名称。
 * @method void setVulName(string $VulName) 设置漏洞名称。
 * @method string getVulType() 获取漏洞类型。
<li> WEB : Web漏洞</li>
<li> SYSTEM :系统组件漏洞</li>
<li> BASELINE : 安全基线</li>
 * @method void setVulType(string $VulType) 设置漏洞类型。
<li> WEB : Web漏洞</li>
<li> SYSTEM :系统组件漏洞</li>
<li> BASELINE : 安全基线</li>
 * @method string getDescription() 获取漏洞描述。
 * @method void setDescription(string $Description) 设置漏洞描述。
 * @method string getVulStatus() 获取漏洞状态。
<li> UN_OPERATED : 待处理</li>
<li> SCANING : 扫描中</li>
<li> FIXED : 已修复</li>
 * @method void setVulStatus(string $VulStatus) 设置漏洞状态。
<li> UN_OPERATED : 待处理</li>
<li> SCANING : 扫描中</li>
<li> FIXED : 已修复</li>
 * @method string getLastScanTime() 获取最后扫描时间。
 * @method void setLastScanTime(string $LastScanTime) 设置最后扫描时间。
 */
class WeeklyReportVul extends AbstractModel
{
    /**
     * @var string 主机内网IP。
     */
    public $MachineIp;

    /**
     * @var string 漏洞名称。
     */
    public $VulName;

    /**
     * @var string 漏洞类型。
<li> WEB : Web漏洞</li>
<li> SYSTEM :系统组件漏洞</li>
<li> BASELINE : 安全基线</li>
     */
    public $VulType;

    /**
     * @var string 漏洞描述。
     */
    public $Description;

    /**
     * @var string 漏洞状态。
<li> UN_OPERATED : 待处理</li>
<li> SCANING : 扫描中</li>
<li> FIXED : 已修复</li>
     */
    public $VulStatus;

    /**
     * @var string 最后扫描时间。
     */
    public $LastScanTime;

    /**
     * @param string $MachineIp 主机内网IP。
     * @param string $VulName 漏洞名称。
     * @param string $VulType 漏洞类型。
<li> WEB : Web漏洞</li>
<li> SYSTEM :系统组件漏洞</li>
<li> BASELINE : 安全基线</li>
     * @param string $Description 漏洞描述。
     * @param string $VulStatus 漏洞状态。
<li> UN_OPERATED : 待处理</li>
<li> SCANING : 扫描中</li>
<li> FIXED : 已修复</li>
     * @param string $LastScanTime 最后扫描时间。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("MachineIp",$param) and $param["MachineIp"] !== null) {
            $this->MachineIp = $param["MachineIp"];
        }

        if (array_key_exists("VulName",$param) and $param["VulName"] !== null) {
            $this->VulName = $param["VulName"];
        }

        if (array_key_exists("VulType",$param) and $param["VulType"] !== null) {
            $this->VulType = $param["VulType"];
        }

        if (array_key_exists("Description",$param) and $param["Description"] !== null) {
            $this->Description = $param["Description"];
        }

        if (array_key_exists("VulStatus",$param) and $param["VulStatus"] !== null) {
            $this->VulStatus = $param["VulStatus"];
        }

        if (array_key_exists("LastScanTime",$param) and $param["LastScanTime"] !== null) {
            $this->LastScanTime = $param["LastScanTime"];
        }
    }
}
