{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "代理保证金退款接口文档", "description": "基于 app/api/controller/User.php 自动生成，适用于Apifox导入。", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "代理保证金管理", "id": 55359879, "auth": {}, "securityScheme": {}, "parentId": 46973024, "serverId": "", "description": "代理保证金退款相关接口", "items": [{"name": "获取代理退款须知", "api": {"id": "288483555", "method": "get", "path": "shopapi/user/getAgentRefundNotice", "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "获取成功"}, "data": {"type": "object", "properties": {"notice": {"type": "string", "description": "退款须知富文本内容", "example": "<p>申请退款须知...</p>"}, "publicity_period_days": {"type": "integer", "description": "公示期天数", "example": 90}}}}}}]}}, {"name": "检查代理退款条件", "api": {"id": "288483556", "method": "get", "path": "shopapi/user/checkAgentRefundCondition", "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "检查完成"}, "data": {"type": "object", "properties": {"can_refund": {"type": "boolean", "description": "是否可以退款", "example": true}, "conditions": {"type": "object", "properties": {"has_deposit": {"type": "boolean", "description": "是否有保证金记录", "example": true}, "no_pending_commission": {"type": "boolean", "description": "是否没有待返佣订单", "example": true}, "no_frozen_commission": {"type": "boolean", "description": "是否没有冻结订单", "example": true}, "no_pending_withdrawal": {"type": "boolean", "description": "是否没有待审核提现", "example": true}}}, "messages": {"type": "array", "description": "提示信息", "items": {"type": "string"}, "example": []}}}}}}]}}, {"name": "申请代理保证金退款", "api": {"id": "288483557", "method": "post", "path": "shopapi/user/applyAgentDepositRefund", "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "退款申请已提交，请等待审核"}, "data": {"type": "object", "example": {}}}}}, {"code": 400, "name": "失败", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 0}, "msg": {"type": "string", "description": "提示信息", "example": "保证金仍在公示期，请于 2023-12-31 后再申请退款"}, "data": {"type": "object", "example": {}}}}}]}}, {"name": "获取代理保证金退款申请状态", "api": {"id": "288483558", "method": "get", "path": "shopapi/user/getAgentRefundStatus", "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "获取成功"}, "data": {"type": "object", "properties": {"has_deposit": {"type": "boolean", "description": "是否有保证金记录", "example": true}, "deposit_id": {"type": "integer", "description": "保证金记录ID", "example": 123}, "amount": {"type": "number", "format": "decimal", "description": "保证金金额", "example": 5000.0}, "status": {"type": "integer", "description": "状态码", "example": 3}, "status_text": {"type": "string", "description": "状态文本", "example": "退款申请中"}, "payment_date": {"type": "string", "format": "date-time", "description": "支付时间", "example": "2023-01-01 12:00:00"}, "can_apply_refund": {"type": "boolean", "description": "是否可以申请退款", "example": false}, "can_confirm_refund": {"type": "boolean", "description": "是否可以确认退款", "example": false}, "days_remaining": {"type": "integer", "description": "剩余天数", "example": 30}, "message": {"type": "string", "description": "提示信息", "example": "退款申请审核中，还需等待30天公示期结束"}}}}}}]}}, {"name": "确认代理保证金退款", "api": {"id": "288483559", "method": "post", "path": "shopapi/user/confirmAgentRefund", "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "退款申请已提交，请等待平台处理"}, "data": {"type": "object", "example": {}}}}}, {"code": 400, "name": "失败", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 0}, "msg": {"type": "string", "description": "提示信息", "example": "退款申请仍在公示期，还需等待15天才能确认退款"}, "data": {"type": "object", "example": {}}}}}]}}]}], "commonParameters": {"parameters": {"header": [{"name": "token", "defaultEnable": true, "type": "string", "id": "EhhmXmBW8X", "description": "登录令牌", "required": true}]}}, "projectSetting": {"id": "5618394", "auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}]}}