<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tcr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 用户配额返回值
 *
 * @method array getLimitInfo() 获取配额信息
 * @method void setLimitInfo(array $LimitInfo) 设置配额信息
 */
class RespLimit extends AbstractModel
{
    /**
     * @var array 配额信息
     */
    public $LimitInfo;

    /**
     * @param array $LimitInfo 配额信息
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("LimitInfo",$param) and $param["LimitInfo"] !== null) {
            $this->LimitInfo = [];
            foreach ($param["LimitInfo"] as $key => $value){
                $obj = new Limit();
                $obj->deserialize($value);
                array_push($this->LimitInfo, $obj);
            }
        }
    }
}
