<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ecm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * Zone信息
 *
 * @method integer getZoneId() 获取ZoneId
 * @method void setZoneId(integer $ZoneId) 设置ZoneId
 * @method string getZoneName() 获取ZoneName
 * @method void setZoneName(string $ZoneName) 设置ZoneName
 * @method string getZone() 获取Zone
 * @method void setZone(string $Zone) 设置Zone
 */
class ZoneInfo extends AbstractModel
{
    /**
     * @var integer ZoneId
     */
    public $ZoneId;

    /**
     * @var string ZoneName
     */
    public $ZoneName;

    /**
     * @var string Zone
     */
    public $Zone;

    /**
     * @param integer $ZoneId ZoneId
     * @param string $ZoneName ZoneName
     * @param string $Zone Zone
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ZoneId",$param) and $param["ZoneId"] !== null) {
            $this->ZoneId = $param["ZoneId"];
        }

        if (array_key_exists("ZoneName",$param) and $param["ZoneName"] !== null) {
            $this->ZoneName = $param["ZoneName"];
        }

        if (array_key_exists("Zone",$param) and $param["Zone"] !== null) {
            $this->Zone = $param["Zone"];
        }
    }
}
