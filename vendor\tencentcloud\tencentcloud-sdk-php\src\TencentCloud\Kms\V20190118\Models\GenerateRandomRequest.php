<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Kms\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * GenerateRandom请求参数结构体
 *
 * @method integer getNumberOfBytes() 获取生成的随机数的长度。最小值为1， 最大值为1024。
 * @method void setNumberOfBytes(integer $NumberOfBytes) 设置生成的随机数的长度。最小值为1， 最大值为1024。
 */
class GenerateRandomRequest extends AbstractModel
{
    /**
     * @var integer 生成的随机数的长度。最小值为1， 最大值为1024。
     */
    public $NumberOfBytes;

    /**
     * @param integer $NumberOfBytes 生成的随机数的长度。最小值为1， 最大值为1024。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("NumberOfBytes",$param) and $param["NumberOfBytes"] !== null) {
            $this->NumberOfBytes = $param["NumberOfBytes"];
        }
    }
}
