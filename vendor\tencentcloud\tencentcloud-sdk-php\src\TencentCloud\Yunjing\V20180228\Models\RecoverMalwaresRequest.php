<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Yunjing\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * RecoverMalwares请求参数结构体
 *
 * @method array getIds() 获取木马Id数组,单次最大删除不能超过200条
 * @method void setIds(array $Ids) 设置木马Id数组,单次最大删除不能超过200条
 */
class RecoverMalwaresRequest extends AbstractModel
{
    /**
     * @var array 木马Id数组,单次最大删除不能超过200条
     */
    public $Ids;

    /**
     * @param array $Ids 木马Id数组,单次最大删除不能超过200条
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Ids",$param) and $param["Ids"] !== null) {
            $this->Ids = $param["Ids"];
        }
    }
}
