# 商家保证金撤销退款接口文档

## 📋 接口概述

**功能描述：** 商家可以撤销已提交的保证金退款申请，撤销后公示期重置，保证金状态恢复为正常状态。

**接口路径：** `POST /shopapi/index/cancelShopDepositRefund`

**请求方式：** POST

**权限要求：** 需要商家登录状态

## 🔧 撤销条件

### 主要条件
- `refund_time` 字段不为空（已设置退款时间）
- 退款状态为申请中（refund_status = 1）或已退款（refund_status = 2）
- 退款未实际处理完成（没有退款备注或未实际到账）

### 不可撤销的情况
- 未找到可撤销的退款记录
- 退款已处理完成且有退款备注
- 退款已实际到账户

## 📝 请求参数

**Content-Type:** `application/json` 或 `application/x-www-form-urlencoded`

**Headers:**
```
token: 商家登录token
```

**Body参数：** 无需额外参数，系统自动根据商家ID查找可撤销的退款记录

## 📤 响应格式

### 成功响应
```json
{
    "code": 1,
    "show": 0,
    "msg": "退款申请已成功撤销",
    "data": {
        "deposit_id": 123,
        "deposit_amount": "1000.00",
        "cancel_time": "2024-12-18 15:30:00"
    }
}
```

### 失败响应
```json
{
    "code": 0,
    "show": 1,
    "msg": "未找到可撤销的退款记录",
    "data": []
}
```

## 🔄 撤销逻辑

### 数据重置
撤销退款时，系统会重置以下字段：

| 字段名 | 重置值 | 说明 |
|--------|--------|------|
| `refund_status` | 0 | 重置为未申请状态 |
| `refund_apply_time` | null | 清空申请时间 |
| `refund_reason` | null | 清空申请原因 |
| `refund_time` | null | 清空退款时间 |
| `refund_remark` | null | 清空退款备注 |
| `refund_admin_id` | null | 清空处理管理员 |
| `refund_publicity_start_time` | null | 清空公示期开始时间 |
| `refund_publicity_end_time` | null | 清空公示期结束时间 |
| `updated_at` | 当前时间 | 更新修改时间 |

### 关联数据处理
- 删除相关的 `ls_common_refund` 表中未处理的退款记录
- 保留已处理完成的退款记录作为历史记录

## 🛡️ 安全机制

### 事务处理
- 使用数据库事务确保数据一致性
- 操作失败时自动回滚所有更改

### 权限验证
- 验证商家登录状态
- 只能撤销自己的退款申请

### 状态检查
- 严格检查退款记录的有效性
- 防止重复撤销或无效操作

## 📊 状态流转

```
正常状态 → 申请退款 → 公示期 → 可撤销
    ↑                              ↓
    ←←←←←←← 撤销退款 ←←←←←←←←←←←←←←←
```

### 状态说明
- **正常状态：** refund_status = 0，可以申请退款
- **申请退款：** refund_status = 1，已提交申请，进入公示期
- **可撤销：** refund_time 不为空，满足撤销条件
- **撤销后：** 恢复到正常状态，可以重新申请退款

## 🔍 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 0 | 请求失败 | 检查具体错误信息 |
| 1 | 请求成功 | 操作完成 |

### 常见错误信息
- "请先登录" - 商家未登录或token无效
- "未找到可撤销的退款记录" - 没有满足条件的退款记录
- "退款已处理完成，无法撤销" - 退款已实际处理，不能撤销
- "撤销退款失败" - 数据库操作失败

## 📋 使用示例

### cURL 示例
```bash
curl -X POST "https://www.huohanghang.cn/shopapi/index/cancelShopDepositRefund" \
  -H "Content-Type: application/json" \
  -H "token: your_shop_token" \
  -d '{}'
```

### JavaScript 示例
```javascript
fetch('/shopapi/index/cancelShopDepositRefund', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'token': 'your_shop_token'
    },
    body: JSON.stringify({})
})
.then(response => response.json())
.then(data => {
    if (data.code === 1) {
        console.log('撤销成功:', data.data);
    } else {
        console.error('撤销失败:', data.msg);
    }
});
```

## 🔗 相关接口

- `POST /shopapi/index/confirmShopDepositRefund` - 确认退款申请
- `GET /shopapi/index/checkShopDepositRefundCondition` - 检查退款条件
- `GET /shopapi/index/getShopDepositRefundNotice` - 获取退款警告信息

## 📝 注意事项

1. **撤销时机：** 只有在退款时间不为空时才能撤销
2. **数据安全：** 使用事务处理确保数据一致性
3. **权限控制：** 只能撤销自己的退款申请
4. **状态恢复：** 撤销后可以重新申请退款
5. **历史记录：** 已处理的退款记录会保留作为历史记录

## 🔄 更新日志

- **2024-12-18：** 新增撤销退款接口
- **2024-12-18：** 添加公示期字段支持
- **2024-12-18：** 完善撤销逻辑和安全机制
