<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Gse\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 服务部署组目的集合
 *
 * @method string getDestinationArn() 获取服务部署组目的的资源描述
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setDestinationArn(string $DestinationArn) 设置服务部署组目的的资源描述
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getFleetStatus() 获取服务部署组目的的状态
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setFleetStatus(string $FleetStatus) 设置服务部署组目的的状态
注意：此字段可能返回 null，表示取不到有效值。
 */
class GameServerSessionQueueDestination extends AbstractModel
{
    /**
     * @var string 服务部署组目的的资源描述
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $DestinationArn;

    /**
     * @var string 服务部署组目的的状态
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $FleetStatus;

    /**
     * @param string $DestinationArn 服务部署组目的的资源描述
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $FleetStatus 服务部署组目的的状态
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("DestinationArn",$param) and $param["DestinationArn"] !== null) {
            $this->DestinationArn = $param["DestinationArn"];
        }

        if (array_key_exists("FleetStatus",$param) and $param["FleetStatus"] !== null) {
            $this->FleetStatus = $param["FleetStatus"];
        }
    }
}
