<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Gse\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 玩家自定义数据
 *
 * @method string getKey() 获取玩家自定义数据键，最小长度不小于1个ASCII字符，最大长度不超过1024个ASCII字符
 * @method void setKey(string $Key) 设置玩家自定义数据键，最小长度不小于1个ASCII字符，最大长度不超过1024个ASCII字符
 * @method string getValue() 获取玩家自定义数据值，最小长度不小于1个ASCII字符，最大长度不超过2048个ASCII字符
 * @method void setValue(string $Value) 设置玩家自定义数据值，最小长度不小于1个ASCII字符，最大长度不超过2048个ASCII字符
 */
class PlayerDataMap extends AbstractModel
{
    /**
     * @var string 玩家自定义数据键，最小长度不小于1个ASCII字符，最大长度不超过1024个ASCII字符
     */
    public $Key;

    /**
     * @var string 玩家自定义数据值，最小长度不小于1个ASCII字符，最大长度不超过2048个ASCII字符
     */
    public $Value;

    /**
     * @param string $Key 玩家自定义数据键，最小长度不小于1个ASCII字符，最大长度不超过1024个ASCII字符
     * @param string $Value 玩家自定义数据值，最小长度不小于1个ASCII字符，最大长度不超过2048个ASCII字符
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Key",$param) and $param["Key"] !== null) {
            $this->Key = $param["Key"];
        }

        if (array_key_exists("Value",$param) and $param["Value"] !== null) {
            $this->Value = $param["Value"];
        }
    }
}
