<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cvm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeImages返回参数结构体
 *
 * @method array getImageSet() 获取一个关于镜像详细信息的结构体，主要包括镜像的主要状态与属性。
 * @method void setImageSet(array $ImageSet) 设置一个关于镜像详细信息的结构体，主要包括镜像的主要状态与属性。
 * @method integer getTotalCount() 获取符合要求的镜像数量。
 * @method void setTotalCount(integer $TotalCount) 设置符合要求的镜像数量。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeImagesResponse extends AbstractModel
{
    /**
     * @var array 一个关于镜像详细信息的结构体，主要包括镜像的主要状态与属性。
     */
    public $ImageSet;

    /**
     * @var integer 符合要求的镜像数量。
     */
    public $TotalCount;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $ImageSet 一个关于镜像详细信息的结构体，主要包括镜像的主要状态与属性。
     * @param integer $TotalCount 符合要求的镜像数量。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ImageSet",$param) and $param["ImageSet"] !== null) {
            $this->ImageSet = [];
            foreach ($param["ImageSet"] as $key => $value){
                $obj = new Image();
                $obj->deserialize($value);
                array_push($this->ImageSet, $obj);
            }
        }

        if (array_key_exists("TotalCount",$param) and $param["TotalCount"] !== null) {
            $this->TotalCount = $param["TotalCount"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
