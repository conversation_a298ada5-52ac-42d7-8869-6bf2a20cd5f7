<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ump\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreateCameraAlerts请求参数结构体
 *
 * @method array getAlerts() 获取告警信息列表
 * @method void setAlerts(array $Alerts) 设置告警信息列表
 */
class CreateCameraAlertsRequest extends AbstractModel
{
    /**
     * @var array 告警信息列表
     */
    public $Alerts;

    /**
     * @param array $Alerts 告警信息列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Alerts",$param) and $param["Alerts"] !== null) {
            $this->Alerts = [];
            foreach ($param["Alerts"] as $key => $value){
                $obj = new CreateCameraAlertAlert();
                $obj->deserialize($value);
                array_push($this->Alerts, $obj);
            }
        }
    }
}
