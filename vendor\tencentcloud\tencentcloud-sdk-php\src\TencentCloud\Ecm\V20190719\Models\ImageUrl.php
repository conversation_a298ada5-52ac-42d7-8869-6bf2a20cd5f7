<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ecm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 镜像文件信息
 *
 * @method string getImageFile() 获取镜像文件COS链接，如设置私有读写，需授权腾讯云ECM运营账号访问权限。
 * @method void setImageFile(string $ImageFile) 设置镜像文件COS链接，如设置私有读写，需授权腾讯云ECM运营账号访问权限。
 */
class ImageUrl extends AbstractModel
{
    /**
     * @var string 镜像文件COS链接，如设置私有读写，需授权腾讯云ECM运营账号访问权限。
     */
    public $ImageFile;

    /**
     * @param string $ImageFile 镜像文件COS链接，如设置私有读写，需授权腾讯云ECM运营账号访问权限。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ImageFile",$param) and $param["ImageFile"] !== null) {
            $this->ImageFile = $param["ImageFile"];
        }
    }
}
