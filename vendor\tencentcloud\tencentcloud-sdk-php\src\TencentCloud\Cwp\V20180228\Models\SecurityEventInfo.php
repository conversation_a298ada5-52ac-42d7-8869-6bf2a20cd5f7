<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cwp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 安全事件统计列表
 *
 * @method integer getEventCnt() 获取安全事件数
 * @method void setEventCnt(integer $EventCnt) 设置安全事件数
 * @method integer getUuidCnt() 获取受影响机器数
 * @method void setUuidCnt(integer $UuidCnt) 设置受影响机器数
 */
class SecurityEventInfo extends AbstractModel
{
    /**
     * @var integer 安全事件数
     */
    public $EventCnt;

    /**
     * @var integer 受影响机器数
     */
    public $UuidCnt;

    /**
     * @param integer $EventCnt 安全事件数
     * @param integer $UuidCnt 受影响机器数
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("EventCnt",$param) and $param["EventCnt"] !== null) {
            $this->EventCnt = $param["EventCnt"];
        }

        if (array_key_exists("UuidCnt",$param) and $param["UuidCnt"] !== null) {
            $this->UuidCnt = $param["UuidCnt"];
        }
    }
}
