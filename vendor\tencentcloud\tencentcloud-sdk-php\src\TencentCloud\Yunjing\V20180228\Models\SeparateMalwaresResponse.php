<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Yunjing\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * SeparateMalwares返回参数结构体
 *
 * @method array getSuccessIds() 获取隔离成功的id数组。
 * @method void setSuccessIds(array $SuccessIds) 设置隔离成功的id数组。
 * @method array getFailedIds() 获取隔离失败的id数组。
 * @method void setFailedIds(array $FailedIds) 设置隔离失败的id数组。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class SeparateMalwaresResponse extends AbstractModel
{
    /**
     * @var array 隔离成功的id数组。
     */
    public $SuccessIds;

    /**
     * @var array 隔离失败的id数组。
     */
    public $FailedIds;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $SuccessIds 隔离成功的id数组。
     * @param array $FailedIds 隔离失败的id数组。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SuccessIds",$param) and $param["SuccessIds"] !== null) {
            $this->SuccessIds = $param["SuccessIds"];
        }

        if (array_key_exists("FailedIds",$param) and $param["FailedIds"] !== null) {
            $this->FailedIds = $param["FailedIds"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
