<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ssl\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ModifyCertificateProject返回参数结构体
 *
 * @method array getSuccessCertificates() 获取修改所属项目成功的证书集合。
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setSuccessCertificates(array $SuccessCertificates) 设置修改所属项目成功的证书集合。
注意：此字段可能返回 null，表示取不到有效值。
 * @method array getFailCertificates() 获取修改所属项目失败的证书集合。
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setFailCertificates(array $FailCertificates) 设置修改所属项目失败的证书集合。
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class ModifyCertificateProjectResponse extends AbstractModel
{
    /**
     * @var array 修改所属项目成功的证书集合。
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $SuccessCertificates;

    /**
     * @var array 修改所属项目失败的证书集合。
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $FailCertificates;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $SuccessCertificates 修改所属项目成功的证书集合。
注意：此字段可能返回 null，表示取不到有效值。
     * @param array $FailCertificates 修改所属项目失败的证书集合。
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SuccessCertificates",$param) and $param["SuccessCertificates"] !== null) {
            $this->SuccessCertificates = $param["SuccessCertificates"];
        }

        if (array_key_exists("FailCertificates",$param) and $param["FailCertificates"] !== null) {
            $this->FailCertificates = $param["FailCertificates"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
