{"apifoxProject": "1.0.0", "info": {"name": "Activity接口文档", "description": "基于 app/shopapi/controller/Activity.php 自动生成，适用于Apifox导入。"}, "apiCollection": [{"name": "集众筹活动模块", "items": [{"name": "集众筹活动列表", "api": {"method": "get", "path": "/shopapi/activity/lists", "parameters": {"query": [{"name": "page", "type": "integer", "description": "页码", "required": false}, {"name": "limit", "type": "integer", "description": "每页数量", "required": false}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "object"}}}}}, {"name": "选择集众筹商品", "api": {"method": "get", "path": "/shopapi/activity/select", "parameters": {"query": [{"name": "keyword", "type": "string", "description": "商品关键字", "required": false}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "array", "items": {"type": "object"}}}}}}, {"name": "数据统计", "api": {"method": "get", "path": "/shopapi/activity/statistics", "parameters": {"query": [{"name": "date", "type": "string", "description": "统计日期", "required": false}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "object"}}}}}, {"name": "集众筹活动详细", "api": {"method": "get", "path": "/shopapi/activity/detail", "parameters": {"query": [{"name": "id", "type": "integer", "description": "活动ID", "required": true}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "object"}}}}}, {"name": "新增集众筹活动", "api": {"method": "post", "path": "/shopapi/activity/add", "parameters": {"body": [{"name": "name", "type": "string", "description": "活动名称", "required": true}, {"name": "start_time", "type": "string", "description": "开始时间", "required": true}, {"name": "end_time", "type": "string", "description": "结束时间", "required": true}, {"name": "goods_ids", "type": "array", "description": "商品ID数组", "required": true}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}}}, {"name": "编辑集众筹活动", "api": {"method": "post", "path": "/shopapi/activity/edit", "parameters": {"body": [{"name": "id", "type": "integer", "description": "活动ID", "required": true}, {"name": "name", "type": "string", "description": "活动名称", "required": false}, {"name": "start_time", "type": "string", "description": "开始时间", "required": false}, {"name": "end_time", "type": "string", "description": "结束时间", "required": false}, {"name": "goods_ids", "type": "array", "description": "商品ID数组", "required": false}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}}}, {"name": "删除集众筹活动", "api": {"method": "post", "path": "/shopapi/activity/del", "parameters": {"body": [{"name": "id", "type": "integer", "description": "活动ID", "required": true}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}}}, {"name": "停止活动", "api": {"method": "post", "path": "/shopapi/activity/stop", "parameters": {"body": [{"name": "id", "type": "integer", "description": "活动ID", "required": true}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}}}, {"name": "开启集众筹活动", "api": {"method": "post", "path": "/shopapi/activity/open", "parameters": {"body": [{"name": "id", "type": "integer", "description": "活动ID", "required": true}]}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}}}}}, {"name": "协议内容", "api": {"method": "get", "path": "/shopapi/activity/agreementContent", "parameters": {}, "response": {"type": "object", "properties": {"code": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "string"}}}}}]}]}