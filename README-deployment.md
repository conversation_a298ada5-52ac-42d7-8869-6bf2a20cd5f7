# kshop项目部署管理方案

## 🎯 方案概述

这套方案解决了测试版到正式版的同步问题，并提供了完整的版本回退机制。

### 核心特性
- ✅ 一键从测试版同步到正式版
- ✅ 支持任意版本回退
- ✅ 自动版本标签管理
- ✅ 安全的备份机制
- ✅ 简单易用的命令行工具

## 🏗️ 架构说明

### 分支管理
```
master/main (正式版) ← 生产环境
    ↑
develop/test (测试版) ← 测试环境
    ↑
feature/* (功能开发)
```

### 服务器目录结构
```
/var/www/
├── kshop-production/     # 正式版目录 (软链接)
├── kshop-test/          # 测试版目录
├── kshop-releases/      # 版本发布目录
│   ├── v1.0.0/
│   ├── v1.0.1/
│   └── v1.0.2/
└── kshop-backups/       # 备份目录
    ├── kshop-backup-20241201_143022/
    └── kshop-backup-20241201_150315/
```

## 🚀 快速开始

### 1. 初始化工作流

```bash
# 设置Git工作流和部署脚本
chmod +x scripts/setup_git_workflow.sh
./scripts/setup_git_workflow.sh
```

### 2. 配置仓库地址

编辑以下文件，将仓库地址替换为实际地址：
- `scripts/deploy.sh`
- `scripts/sync_test_to_prod.sh`

```bash
# 修改这行为实际的仓库地址
REPO_URL="https://github.com/your-username/kshop.git"
```

### 3. 开始使用

```bash
# 切换到开发分支
git checkout develop

# 开发完成后，部署到测试环境
./scripts/deploy.sh test

# 测试通过后，同步到正式环境
./scripts/sync_test_to_prod.sh
```

## 📋 常用命令

### 部署相关

```bash
# 部署到测试环境
./scripts/deploy.sh test

# 部署指定版本到正式环境
./scripts/deploy.sh production v1.0.1

# 一键同步测试版到正式版
./scripts/sync_test_to_prod.sh

# 指定版本号同步
./scripts/sync_test_to_prod.sh v1.0.2
```

### 版本管理

```bash
# 查看当前版本
./scripts/version_manager.sh current

# 列出所有可用版本
./scripts/version_manager.sh list

# 交互式版本回退
./scripts/version_manager.sh rollback

# 清理旧版本（保留最新5个）
./scripts/version_manager.sh cleanup

# 清理旧版本（保留最新3个）
./scripts/version_manager.sh cleanup 3
```

### 版本回退

```bash
# 方法1: 使用版本管理脚本（推荐）
./scripts/version_manager.sh rollback

# 方法2: 直接使用部署脚本
./scripts/deploy.sh rollback v1.0.0
```

## 🔄 完整工作流程

### 日常开发流程

1. **功能开发**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/new-feature
   
   # 开发代码...
   
   git add .
   git commit -m "feat(module): 添加新功能"
   git push origin feature/new-feature
   ```

2. **合并到测试分支**
   ```bash
   git checkout develop
   git merge feature/new-feature
   git push origin develop
   ```

3. **部署测试环境**
   ```bash
   ./scripts/deploy.sh test
   ```

4. **测试验证**
   - 在测试环境验证功能
   - 确认无问题后进行下一步

5. **发布到正式环境**
   ```bash
   ./scripts/sync_test_to_prod.sh
   ```

### 紧急回退流程

如果正式环境出现问题：

```bash
# 查看可用版本
./scripts/version_manager.sh list

# 交互式回退
./scripts/version_manager.sh rollback

# 或直接回退到指定版本
./scripts/deploy.sh rollback v1.0.0
```

## 🛡️ 安全特性

### 自动备份
- 每次部署前自动备份当前版本
- 保留最近10个备份文件
- 备份文件包含完整的项目代码

### 版本验证
- 部署前检查Git状态
- 确认操作前需要用户确认
- 支持操作取消

### 权限管理
- 自动设置正确的文件权限
- 支持www-data用户权限

## 🔧 自定义配置

### 修改保留版本数量

编辑 `scripts/version_manager.sh`：
```bash
local keep_count=${1:-5}  # 修改默认保留数量
```

### 修改服务器路径

编辑 `scripts/deploy.sh`：
```bash
BASE_DIR="/var/www"                    # 修改基础目录
TEST_DIR="$BASE_DIR/${PROJECT_NAME}-test"
PROD_DIR="$BASE_DIR/${PROJECT_NAME}-production"
```

### 添加自定义部署步骤

在 `setup_environment()` 函数中添加：
```bash
# 自定义配置
sudo cp custom-config.php "$target_dir/config/"

# 重启自定义服务
sudo systemctl restart custom-service
```

## 🐛 故障排除

### 常见问题

1. **权限问题**
   ```bash
   sudo chown -R www-data:www-data /var/www/kshop-*
   sudo chmod +x scripts/*.sh
   ```

2. **Git冲突**
   ```bash
   git status
   git stash
   git pull origin develop
   git stash pop
   ```

3. **部署失败**
   ```bash
   # 查看详细错误信息
   bash -x scripts/deploy.sh test
   
   # 检查服务状态
   sudo systemctl status nginx
   sudo systemctl status php7.4-fpm
   ```

### 日志查看

```bash
# Nginx日志
sudo tail -f /var/log/nginx/error.log

# PHP日志
sudo tail -f /var/log/php7.4-fpm.log

# 系统日志
sudo journalctl -f
```

## 📞 支持

如果遇到问题，请检查：
1. 服务器权限设置
2. Git仓库配置
3. 网络连接状态
4. 磁盘空间

更多详细信息请查看 `docs/git-workflow.md`
