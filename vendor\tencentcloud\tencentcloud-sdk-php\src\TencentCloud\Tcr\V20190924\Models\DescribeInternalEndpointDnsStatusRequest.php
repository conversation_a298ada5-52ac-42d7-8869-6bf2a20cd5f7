<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tcr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeInternalEndpointDnsStatus请求参数结构体
 *
 * @method array getVpcSet() 获取vpc列表
 * @method void setVpcSet(array $VpcSet) 设置vpc列表
 */
class DescribeInternalEndpointDnsStatusRequest extends AbstractModel
{
    /**
     * @var array vpc列表
     */
    public $VpcSet;

    /**
     * @param array $VpcSet vpc列表
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("VpcSet",$param) and $param["VpcSet"] !== null) {
            $this->VpcSet = [];
            foreach ($param["VpcSet"] as $key => $value){
                $obj = new VpcAndDomainInfo();
                $obj->deserialize($value);
                array_push($this->VpcSet, $obj);
            }
        }
    }
}
