<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ie\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 帧标签结果
 *
 * @method array getFrameTagItems() 获取帧标签结果数组
 * @method void setFrameTagItems(array $FrameTagItems) 设置帧标签结果数组
 */
class FrameTagResult extends AbstractModel
{
    /**
     * @var array 帧标签结果数组
     */
    public $FrameTagItems;

    /**
     * @param array $FrameTagItems 帧标签结果数组
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("FrameTagItems",$param) and $param["FrameTagItems"] !== null) {
            $this->FrameTagItems = [];
            foreach ($param["FrameTagItems"] as $key => $value){
                $obj = new FrameTagItem();
                $obj->deserialize($value);
                array_push($this->FrameTagItems, $obj);
            }
        }
    }
}
