<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Domain\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 批量操作记录
 *
 * @method integer getLogId() 获取日志ID
 * @method void setLogId(integer $LogId) 设置日志ID
 * @method integer getNumber() 获取数量
 * @method void setNumber(integer $Number) 设置数量
 * @method string getStatus() 获取执行状态：
doing 执行中。
done 执行完成。
 * @method void setStatus(string $Status) 设置执行状态：
doing 执行中。
done 执行完成。
 * @method string getCreatedOn() 获取提交时间
 * @method void setCreatedOn(string $CreatedOn) 设置提交时间
 */
class DomainBatchLogSet extends AbstractModel
{
    /**
     * @var integer 日志ID
     */
    public $LogId;

    /**
     * @var integer 数量
     */
    public $Number;

    /**
     * @var string 执行状态：
doing 执行中。
done 执行完成。
     */
    public $Status;

    /**
     * @var string 提交时间
     */
    public $CreatedOn;

    /**
     * @param integer $LogId 日志ID
     * @param integer $Number 数量
     * @param string $Status 执行状态：
doing 执行中。
done 执行完成。
     * @param string $CreatedOn 提交时间
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("LogId",$param) and $param["LogId"] !== null) {
            $this->LogId = $param["LogId"];
        }

        if (array_key_exists("Number",$param) and $param["Number"] !== null) {
            $this->Number = $param["Number"];
        }

        if (array_key_exists("Status",$param) and $param["Status"] !== null) {
            $this->Status = $param["Status"];
        }

        if (array_key_exists("CreatedOn",$param) and $param["CreatedOn"] !== null) {
            $this->CreatedOn = $param["CreatedOn"];
        }
    }
}
