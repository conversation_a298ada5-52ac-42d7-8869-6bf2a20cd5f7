<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dayu\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeCCFrequencyRules返回参数结构体
 *
 * @method array getCCFrequencyRuleList() 获取访问频率控制规则列表
 * @method void setCCFrequencyRuleList(array $CCFrequencyRuleList) 设置访问频率控制规则列表
 * @method string getCCFrequencyRuleStatus() 获取访问频率控制规则开关状态，取值[on(开启)，off(关闭)]
 * @method void setCCFrequencyRuleStatus(string $CCFrequencyRuleStatus) 设置访问频率控制规则开关状态，取值[on(开启)，off(关闭)]
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeCCFrequencyRulesResponse extends AbstractModel
{
    /**
     * @var array 访问频率控制规则列表
     */
    public $CCFrequencyRuleList;

    /**
     * @var string 访问频率控制规则开关状态，取值[on(开启)，off(关闭)]
     */
    public $CCFrequencyRuleStatus;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $CCFrequencyRuleList 访问频率控制规则列表
     * @param string $CCFrequencyRuleStatus 访问频率控制规则开关状态，取值[on(开启)，off(关闭)]
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("CCFrequencyRuleList",$param) and $param["CCFrequencyRuleList"] !== null) {
            $this->CCFrequencyRuleList = [];
            foreach ($param["CCFrequencyRuleList"] as $key => $value){
                $obj = new CCFrequencyRule();
                $obj->deserialize($value);
                array_push($this->CCFrequencyRuleList, $obj);
            }
        }

        if (array_key_exists("CCFrequencyRuleStatus",$param) and $param["CCFrequencyRuleStatus"] !== null) {
            $this->CCFrequencyRuleStatus = $param["CCFrequencyRuleStatus"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
