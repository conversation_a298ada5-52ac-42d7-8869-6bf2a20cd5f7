<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Sqlserver\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 地域信息
 *
 * @method string getRegion() 获取地域英文ID，类似ap-guanghou
 * @method void setRegion(string $Region) 设置地域英文ID，类似ap-guanghou
 * @method string getRegionName() 获取地域中文名称
 * @method void setRegionName(string $RegionName) 设置地域中文名称
 * @method integer getRegionId() 获取地域数字ID
 * @method void setRegionId(integer $RegionId) 设置地域数字ID
 * @method string getRegionState() 获取该地域目前是否可以售卖，UNAVAILABLE-不可售卖；AVAILABLE-可售卖
 * @method void setRegionState(string $RegionState) 设置该地域目前是否可以售卖，UNAVAILABLE-不可售卖；AVAILABLE-可售卖
 */
class RegionInfo extends AbstractModel
{
    /**
     * @var string 地域英文ID，类似ap-guanghou
     */
    public $Region;

    /**
     * @var string 地域中文名称
     */
    public $RegionName;

    /**
     * @var integer 地域数字ID
     */
    public $RegionId;

    /**
     * @var string 该地域目前是否可以售卖，UNAVAILABLE-不可售卖；AVAILABLE-可售卖
     */
    public $RegionState;

    /**
     * @param string $Region 地域英文ID，类似ap-guanghou
     * @param string $RegionName 地域中文名称
     * @param integer $RegionId 地域数字ID
     * @param string $RegionState 该地域目前是否可以售卖，UNAVAILABLE-不可售卖；AVAILABLE-可售卖
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Region",$param) and $param["Region"] !== null) {
            $this->Region = $param["Region"];
        }

        if (array_key_exists("RegionName",$param) and $param["RegionName"] !== null) {
            $this->RegionName = $param["RegionName"];
        }

        if (array_key_exists("RegionId",$param) and $param["RegionId"] !== null) {
            $this->RegionId = $param["RegionId"];
        }

        if (array_key_exists("RegionState",$param) and $param["RegionState"] !== null) {
            $this->RegionState = $param["RegionState"];
        }
    }
}
