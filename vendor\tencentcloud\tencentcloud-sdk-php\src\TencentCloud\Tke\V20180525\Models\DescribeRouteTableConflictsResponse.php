<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tke\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeRouteTableConflicts返回参数结构体
 *
 * @method boolean getHasConflict() 获取路由表是否冲突。
 * @method void setHasConflict(boolean $HasConflict) 设置路由表是否冲突。
 * @method array getRouteTableConflictSet() 获取路由表冲突列表。
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setRouteTableConflictSet(array $RouteTableConflictSet) 设置路由表冲突列表。
注意：此字段可能返回 null，表示取不到有效值。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeRouteTableConflictsResponse extends AbstractModel
{
    /**
     * @var boolean 路由表是否冲突。
     */
    public $HasConflict;

    /**
     * @var array 路由表冲突列表。
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $RouteTableConflictSet;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param boolean $HasConflict 路由表是否冲突。
     * @param array $RouteTableConflictSet 路由表冲突列表。
注意：此字段可能返回 null，表示取不到有效值。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("HasConflict",$param) and $param["HasConflict"] !== null) {
            $this->HasConflict = $param["HasConflict"];
        }

        if (array_key_exists("RouteTableConflictSet",$param) and $param["RouteTableConflictSet"] !== null) {
            $this->RouteTableConflictSet = [];
            foreach ($param["RouteTableConflictSet"] as $key => $value){
                $obj = new RouteTableConflict();
                $obj->deserialize($value);
                array_push($this->RouteTableConflictSet, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
