<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tbaas\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 组织详情信息
 *
 * @method string getGroupName() 获取组织名称
 * @method void setGroupName(string $GroupName) 设置组织名称
 * @method string getGroupMSPId() 获取组织MSP Identity
 * @method void setGroupMSPId(string $GroupMSPId) 设置组织MSP Identity
 */
class GroupDetailForUser extends AbstractModel
{
    /**
     * @var string 组织名称
     */
    public $GroupName;

    /**
     * @var string 组织MSP Identity
     */
    public $GroupMSPId;

    /**
     * @param string $GroupName 组织名称
     * @param string $GroupMSPId 组织MSP Identity
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("GroupName",$param) and $param["GroupName"] !== null) {
            $this->GroupName = $param["GroupName"];
        }

        if (array_key_exists("GroupMSPId",$param) and $param["GroupMSPId"] !== null) {
            $this->GroupMSPId = $param["GroupMSPId"];
        }
    }
}
