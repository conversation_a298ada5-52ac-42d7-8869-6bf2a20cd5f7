<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Gpm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeRules返回参数结构体
 *
 * @method array getRuleInfoList() 获取规则信息列表
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setRuleInfoList(array $RuleInfoList) 设置规则信息列表
注意：此字段可能返回 null，表示取不到有效值。
 * @method integer getTotalCount() 获取总记录数
 * @method void setTotalCount(integer $TotalCount) 设置总记录数
 * @method integer getPageNumber() 获取当前页号
 * @method void setPageNumber(integer $PageNumber) 设置当前页号
 * @method integer getPageSize() 获取单页大小
 * @method void setPageSize(integer $PageSize) 设置单页大小
 * @method string getSearchType() 获取查询类型（可选）matchName表示匹配名称，matchCode表示匹配code，ruleName表示规则名称，tag表示标签Key/Value
 * @method void setSearchType(string $SearchType) 设置查询类型（可选）matchName表示匹配名称，matchCode表示匹配code，ruleName表示规则名称，tag表示标签Key/Value
 * @method string getKeyword() 获取查询关键词（可选）
 * @method void setKeyword(string $Keyword) 设置查询关键词（可选）
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeRulesResponse extends AbstractModel
{
    /**
     * @var array 规则信息列表
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $RuleInfoList;

    /**
     * @var integer 总记录数
     */
    public $TotalCount;

    /**
     * @var integer 当前页号
     */
    public $PageNumber;

    /**
     * @var integer 单页大小
     */
    public $PageSize;

    /**
     * @var string 查询类型（可选）matchName表示匹配名称，matchCode表示匹配code，ruleName表示规则名称，tag表示标签Key/Value
     */
    public $SearchType;

    /**
     * @var string 查询关键词（可选）
     */
    public $Keyword;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $RuleInfoList 规则信息列表
注意：此字段可能返回 null，表示取不到有效值。
     * @param integer $TotalCount 总记录数
     * @param integer $PageNumber 当前页号
     * @param integer $PageSize 单页大小
     * @param string $SearchType 查询类型（可选）matchName表示匹配名称，matchCode表示匹配code，ruleName表示规则名称，tag表示标签Key/Value
     * @param string $Keyword 查询关键词（可选）
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("RuleInfoList",$param) and $param["RuleInfoList"] !== null) {
            $this->RuleInfoList = [];
            foreach ($param["RuleInfoList"] as $key => $value){
                $obj = new RuleBriefInfo();
                $obj->deserialize($value);
                array_push($this->RuleInfoList, $obj);
            }
        }

        if (array_key_exists("TotalCount",$param) and $param["TotalCount"] !== null) {
            $this->TotalCount = $param["TotalCount"];
        }

        if (array_key_exists("PageNumber",$param) and $param["PageNumber"] !== null) {
            $this->PageNumber = $param["PageNumber"];
        }

        if (array_key_exists("PageSize",$param) and $param["PageSize"] !== null) {
            $this->PageSize = $param["PageSize"];
        }

        if (array_key_exists("SearchType",$param) and $param["SearchType"] !== null) {
            $this->SearchType = $param["SearchType"];
        }

        if (array_key_exists("Keyword",$param) and $param["Keyword"] !== null) {
            $this->Keyword = $param["Keyword"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
