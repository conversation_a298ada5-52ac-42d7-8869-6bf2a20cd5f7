{layout name="layout1" /}

<div class="layui-card">
    <div class="layui-card-header">
        <h3>上传资质</h3>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" lay-filter="upload-form">
            <div class="layui-form-item">
                <label class="layui-form-label">资质类型</label>
                <div class="layui-input-block">
                    <select name="qualification_id" lay-verify="required" lay-search lay-filter="qualification-select">
                        <option value="">请选择资质类型</option>
                        {volist name="qualifications" id="qualification"}
                        <option value="{$qualification.id}"
                                data-description="{$qualification.description}"
                                data-document-path="{$qualification.document_path}"
                                data-document-name="{$qualification.document_name}"
                        data-ex-img="{$qualification.ex_img}">{$qualification.name}</option>
                        {/volist}
                    </select>
                </div>
            </div>

            <!-- 资质信息显示区域 -->
            <div class="layui-form-item" id="qualification-info" style="display: none;">
                <label class="layui-form-label"></label>
                <div class="layui-input-block">
                    <div class="qualification-info-container">
                        <div id="qualification-description"></div>
                        <div id="qualification-template"></div>
                        <div id="qualification-example"></div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">资质图片</label>
                <div class="layui-input-block">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn layui-btn-normal" id="upload-document">选择图片</button>
                        <div class="layui-upload-list">
                            <div id="document-list"></div>
                        </div>
                        <input type="hidden" name="document_path" id="document_path" lay-verify="required">
                        <input type="hidden" name="document_name" id="document_name">
                    </div>
                    <div class="layui-form-mid layui-word-aux">支持格式：jpg、jpeg、png、gif、bmp等图片格式，大小不超过5MB</div>
                </div>
            </div>

            <div class="layui-form-item" style="display:none;">
                <label class="layui-form-label">有效期</label>
                <div class="layui-input-block">
                    <input type="text" name="expire_time" placeholder="请选择有效期（可选，不选择表示永久有效）" class="layui-input" id="expire-time">
                    <div class="layui-form-mid layui-word-aux">不选择表示永久有效</div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submit">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 资质信息样式 -->
<style>
.qualification-info-container {
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
}

.qualification-description {
    color: #666;
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 10px;
}

.qualification-template {
    margin-top: 8px;
}

.template-download-btn {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    color: #1890ff;
    text-decoration: none;
    font-size: 13px;
    transition: all 0.3s;
}

.template-download-btn:hover {
    background: #bae7ff;
    border-color: #69c0ff;
    color: #096dd9;
    text-decoration: none;
}

.template-download-btn i {
    margin-right: 6px;
}

/* 示例按钮样式 */
.qualification-example-btn {
    background: #52c41a;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
}

.qualification-example-btn:hover {
    background: #73d13d;
    transform: translateY(-1px);
}

.qualification-example-btn i {
    margin-right: 5px;
}
</style>

<script>
layui.use(['form', 'upload', 'laydate', 'layer'], function(){
    var form = layui.form;
    var upload = layui.upload;
    var laydate = layui.laydate;
    var layer = layui.layer;
    var $ = layui.$;

    // 日期选择器
    laydate.render({
        elem: '#expire-time',
        type: 'datetime',
        min: 0 // 不能选择今天之前的日期
    });

    // 监听资质类型选择
    form.on('select(qualification-select)', function(data){
        var value = data.value;
        var elem = data.elem;

        if (value) {
            var option = $(elem).find('option:selected');
            var description = option.data('description');
            var documentPath = option.data('document-path');
            var documentName = option.data('document-name');
            var exImg = option.data('ex-img');

            showQualificationInfo(description, documentPath, documentName, exImg);
        } else {
            hideQualificationInfo();
        }
    });

    // 图片上传
    upload.render({
        elem: '#upload-document',
        url: '{:url("Upload/document")}',
        accept: 'images',
        exts: 'jpg|jpeg|png|gif|bmp',
        size: 5120, // 5MB
        done: function(res){
            if(res.code === 1) {
                $('#document_path').val(res.data.uri);
                $('#document_name').val(res.data.name);

                // 后端已经返回完整URL，直接使用
                var imageUrl = res.data.uri;

                $('#document-list').html('<p style="color: #5FB878;"><i class="layui-icon layui-icon-picture"></i> ' + res.data.name + ' <a href="javascript:;" onclick="removeDocument()" style="color: #FF5722; margin-left: 10px;">[删除]</a></p><div style="margin-top: 10px;"><img src="' + imageUrl + '" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';"><div style="display:none; color: #999; text-align: center; padding: 20px; border: 1px dashed #ddd;">图片加载失败</div></div>');
                layer.msg('图片上传成功');
            } else {
                layer.msg(res.msg || '上传失败');
            }
        },
        error: function(){
            layer.msg('上传失败');
        }
    });

    // 表单提交
    form.on('submit(submit)', function(data){
        var loadIndex = layer.load(2);

        $.post('{:url("shop_qualification/upload")}', data.field, function(res){
            layer.close(loadIndex);
            if(res.code === 1){
                layer.msg('上传成功', {icon: 1}, function(){
                    // 判断是否在弹窗中
                    try {
                        if (window.parent && window.parent !== window && window.parent.layer) {
                            // 在弹窗中，关闭弹窗并刷新父页面
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                            // 刷新父页面的表格
                            if (parent.layui && parent.layui.table) {
                                parent.layui.table.reload('qualification-table');
                            }
                        } else {
                            // 不在弹窗中，直接跳转
                            location.href = '{:url("shop_qualification/lists")}';
                        }
                    } catch (e) {
                        // 如果出现跨域等错误，默认跳转到列表页
                        console.log('弹窗检测失败，跳转到列表页:', e);
                        location.href = '{:url("shop_qualification/lists")}';
                    }
                });
            } else {
                layer.msg(res.msg || '上传失败', {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.close(loadIndex);
            layer.msg('请求失败：' + error, {icon: 2});
        });

        return false;
    });

    // 显示资质信息
    function showQualificationInfo(description, documentPath, documentName, exImg) {
        var html = '';

        if (description) {
            html += '<div class="qualification-description">' +
                '<strong>资质说明：</strong>' + description +
                '</div>';
        }

        if (documentPath && documentName) {
            html += '<div class="qualification-template">' +
                '<a href="' + documentPath + '" target="_blank" class="template-download-btn">' +
                '<i class="layui-icon layui-icon-download-circle"></i>下载承诺书模板' +
                '</a>' +
                '</div>';
        }

        if (exImg) {
            html += '<div class="qualification-example" style="margin-top: 10px;">' +
                '<button type="button" class="qualification-example-btn" onclick="showExampleImage(\'' + exImg + '\', \'' + $('#qualification-select option:selected').text() + '\')">' +
                '<i class="layui-icon layui-icon-picture"></i>查看示例' +
                '</button>' +
                '</div>';
        }

        if (html) {
            $('#qualification-description').html(description ? '<div class="qualification-description"><strong>资质说明：</strong>' + description + '</div>' : '');
            $('#qualification-template').html(documentPath && documentName ?
                '<div class="qualification-template">' +
                '<a href="' + documentPath + '" target="_blank" class="template-download-btn">' +
                '<i class="layui-icon layui-icon-download-circle"></i>下载承诺书模板' +
                '</a>' +
                '</div>' : '');
            $('#qualification-example').html(exImg ?
                '<div class="qualification-example" style="margin-top: 10px;">' +
                '<button type="button" class="qualification-example-btn" onclick="showExampleImage(\'' + exImg + '\', \'' + $('#qualification-select option:selected').text() + '\')">' +
                '<i class="layui-icon layui-icon-picture"></i>查看示例' +
                '</button>' +
                '</div>' : '');
            $('#qualification-info').show();
        } else {
            hideQualificationInfo();
        }
    }

    // 隐藏资质信息
    function hideQualificationInfo() {
        $('#qualification-info').hide();
    }

    // 如果有传入的qualification_id，自动选中
    var urlParams = new URLSearchParams(window.location.search);
    var qualificationId = urlParams.get('qualification_id');
    if(qualificationId) {
        $('select[name="qualification_id"]').val(qualificationId);
        form.render('select');
        // 触发选择事件显示资质信息
        var option = $('select[name="qualification_id"] option:selected');
        if (option.length > 0) {
            var description = option.data('description');
            var documentPath = option.data('document-path');
            var documentName = option.data('document-name');
            var exImg = option.data('ex-img');
            showQualificationInfo(description, documentPath, documentName, exImg);
        }
    }
});

// 删除文档
function removeDocument() {
    $('#document_path').val('');
    $('#document_name').val('');
    $('#document-list').html('');
    layer.msg('文档已删除');
}

// 显示示例图片
function showExampleImage(imagePath, qualificationName) {
    if (!imagePath) {
        layui.layer.msg('暂无示例图片', { icon: 2 });
        return;
    }

    // 使用layer.open显示图片，控制大小
    layui.layer.open({
        type: 1,
        title: qualificationName + ' - 示例图片',
        area: ['600px', '450px'],
        maxmin: true,
        shadeClose: true,
        content: '<div style="text-align: center; padding: 20px;"><img src="' + imagePath + '" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="示例图片"></div>'
    });
}
</script>
