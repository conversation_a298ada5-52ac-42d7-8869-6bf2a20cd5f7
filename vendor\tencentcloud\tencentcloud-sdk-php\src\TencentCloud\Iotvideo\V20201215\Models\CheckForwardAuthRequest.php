<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Iotvideo\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CheckForwardAuth请求参数结构体
 *
 * @method string getSkey() 获取控制台Skey
 * @method void setSkey(string $Skey) 设置控制台Skey
 * @method integer getQueueType() 获取队列类型
 * @method void setQueueType(integer $QueueType) 设置队列类型
 */
class CheckForwardAuthRequest extends AbstractModel
{
    /**
     * @var string 控制台Skey
     */
    public $Skey;

    /**
     * @var integer 队列类型
     */
    public $QueueType;

    /**
     * @param string $Skey 控制台Skey
     * @param integer $QueueType 队列类型
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Skey",$param) and $param["Skey"] !== null) {
            $this->Skey = $param["Skey"];
        }

        if (array_key_exists("QueueType",$param) and $param["QueueType"] !== null) {
            $this->QueueType = $param["QueueType"];
        }
    }
}
