{"servers": {"context7": {"type": "stdio", "command": "npx", "args": ["-y", "--node-options=--experimental-vm-modules", "@upstash/context7-mcp@latest"]}, "mysql": {"type": "stdio", "command": "uvx", "args": ["--from", "mysql-mcp-server", "mysql_mcp_server"], "env": {"MYSQL_HOST": "*************", "MYSQL_PORT": "3306", "MYSQL_USER": "kshop", "MYSQL_PASSWORD": "DetPwbd6YrtMasHf", "MYSQL_DATABASE": "kshop"}}, "mysql_python": {"type": "stdio", "command": "python", "args": ["-m", "mysql_mcp_server"], "env": {"MYSQL_HOST": "*************", "MYSQL_PORT": "3306", "MYSQL_USER": "kshop", "MYSQL_PASSWORD": "DetPwbd6YrtMasHf", "MYSQL_DATABASE": "kshop"}}, "browser-tools-mcp": {"type": "stdio", "command": "node", "args": ["node_modules/@agentdeskai/browser-tools-mcp/dist/mcp-server.js", "--port", "9222"]}, "ssh-mpc-server": {"type": "stdio", "command": "npx", "args": ["-y", "@fangjunjie/ssh-mcp-server", "--host", "*************", "--port", "22", "--username", "root", "--password", "huohang2024!"]}}}