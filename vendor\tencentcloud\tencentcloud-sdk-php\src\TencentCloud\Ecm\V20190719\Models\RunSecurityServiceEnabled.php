<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ecm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 云镜服务；
 *
 * @method boolean getEnabled() 获取是否开启。
 * @method void setEnabled(boolean $Enabled) 设置是否开启。
 * @method integer getVersion() 获取云镜版本：0 基础版，1 专业版。目前仅支持基础版
 * @method void setVersion(integer $Version) 设置云镜版本：0 基础版，1 专业版。目前仅支持基础版
 */
class RunSecurityServiceEnabled extends AbstractModel
{
    /**
     * @var boolean 是否开启。
     */
    public $Enabled;

    /**
     * @var integer 云镜版本：0 基础版，1 专业版。目前仅支持基础版
     */
    public $Version;

    /**
     * @param boolean $Enabled 是否开启。
     * @param integer $Version 云镜版本：0 基础版，1 专业版。目前仅支持基础版
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Enabled",$param) and $param["Enabled"] !== null) {
            $this->Enabled = $param["Enabled"];
        }

        if (array_key_exists("Version",$param) and $param["Version"] !== null) {
            $this->Version = $param["Version"];
        }
    }
}
