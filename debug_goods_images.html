<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品图片功能调试</title>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .debug-info { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .like-upload-image { display: flex; flex-wrap: wrap; gap: 10px; border: 2px dashed #ddd; padding: 20px; border-radius: 8px; }
        .upload-image-div { position: relative; width: 120px; height: 120px; border: 1px solid #ddd; border-radius: 6px; overflow: hidden; cursor: move; transition: all 0.3s ease; }
        .upload-image-div:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }
        .upload-image-div img { width: 100%; height: 100%; object-fit: cover; }
        .del-upload-btn { position: absolute; top: 5px; right: 5px; width: 20px; height: 20px; background: rgba(255, 0, 0, 0.8); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 12px; }
        .upload-image-elem { width: 120px; height: 120px; border: 2px dashed #ccc; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; }
        .upload-image-elem:hover { border-color: #1890ff; background: #f0f8ff; }
        .add-upload-image { color: #666; text-decoration: none; font-size: 14px; }
        
        /* 图片控制按钮 */
        .image-controls { position: absolute; top: 5px; right: 30px; display: flex; gap: 2px; opacity: 0; transition: opacity 0.3s ease; z-index: 3; }
        .upload-image-div:hover .image-controls { opacity: 1; }
        .image-controls button { width: 20px; height: 20px; border: none; border-radius: 3px; background: rgba(0, 0, 0, 0.7); color: white; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 10px; transition: all 0.3s ease; }
        .image-controls button:hover { background: rgba(0, 0, 0, 0.9); transform: scale(1.1); }
        .btn-set-main { background: #ff4d4f !important; }
        .btn-set-main:hover { background: #ff7875 !important; }
        
        /* 拖拽排序样式 */
        .sortable-ghost { opacity: 0.5; background: #f0f8ff; border: 2px dashed #1890ff; }
        .sortable-chosen { transform: rotate(2deg); }
        .sortable-drag { transform: rotate(2deg); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); }
        
        /* 图片序号显示 */
        .upload-image-div[data-order]::before { content: attr(data-order); position: absolute; top: 5px; left: 5px; background: #1890ff; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 11px; font-weight: bold; z-index: 1; }
        
        /* 主图标识 */
        .upload-image-div[data-order="1"]::after { content: "主图"; position: absolute; bottom: 5px; left: 5px; background: #ff4d4f; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: bold; z-index: 1; }
        
        .btn { padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #40a9ff; }
        .btn-danger { background: #ff4d4f; }
        .btn-danger:hover { background: #ff7875; }
    </style>
</head>
<body>
    <div class="container">
        <h1>商品图片功能调试页面</h1>
        
        <div class="debug-info">
            <h3>当前状态：</h3>
            <div id="status">等待初始化...</div>
        </div>
        
        <div id="goodsImageContainer">
            <div class="like-upload-image sortable-upload-container">
                <!-- 模拟编辑页面的图片数据 -->
                <div class="upload-image-div" data-order="1">
                    <img src="https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=主图" alt="img" class="preview-image" data-src="https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=主图" title="点击放大查看">
                    <input type="hidden" name="goods_image[]" value="https://via.placeholder.com/300x300/FF6B6B/FFFFFF?text=主图">
                    <div class="del-upload-btn">×</div>
                    <div class="image-controls"></div>
                </div>
                <div class="upload-image-div" data-order="2">
                    <img src="https://via.placeholder.com/300x300/4ECDC4/FFFFFF?text=图片2" alt="img" class="preview-image" data-src="https://via.placeholder.com/300x300/4ECDC4/FFFFFF?text=图片2" title="点击放大查看">
                    <input type="hidden" name="goods_image[]" value="https://via.placeholder.com/300x300/4ECDC4/FFFFFF?text=图片2">
                    <div class="del-upload-btn">×</div>
                    <div class="image-controls"></div>
                </div>
                <div class="upload-image-div" data-order="3">
                    <img src="https://via.placeholder.com/300x300/45B7D1/FFFFFF?text=图片3" alt="img" class="preview-image" data-src="https://via.placeholder.com/300x300/45B7D1/FFFFFF?text=图片3" title="点击放大查看">
                    <input type="hidden" name="goods_image[]" value="https://via.placeholder.com/300x300/45B7D1/FFFFFF?text=图片3">
                    <div class="del-upload-btn">×</div>
                    <div class="image-controls"></div>
                </div>
                <div class="upload-image-elem">
                    <a class="add-upload-image" id="goodsimage"> + 添加图片</a>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button class="btn" onclick="addTestImage()">添加测试图片</button>
            <button class="btn" onclick="checkStatus()">检查状态</button>
            <button class="btn" onclick="reinitialize()">重新初始化</button>
            <button class="btn btn-danger" onclick="clearLogs()">清空日志</button>
        </div>
        
        <div class="debug-info">
            <h3>调试日志：</h3>
            <div id="logs" style="max-height: 300px; overflow-y: auto; background: white; padding: 10px; border: 1px solid #ddd;"></div>
        </div>
    </div>

    <script>
        var testImageIndex = 4;
        var logs = [];
        
        function log(message) {
            var timestamp = new Date().toLocaleTimeString();
            logs.push('[' + timestamp + '] ' + message);
            updateLogDisplay();
            console.log(message);
        }
        
        function updateLogDisplay() {
            $('#logs').html(logs.join('<br>'));
            $('#logs').scrollTop($('#logs')[0].scrollHeight);
        }
        
        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }
        
        // 初始化拖拽排序功能
        function initImageSortable() {
            var container = document.querySelector('#goodsImageContainer .like-upload-image');
            if (container && typeof Sortable !== 'undefined') {
                // 销毁已存在的Sortable实例
                if (container.sortableInstance) {
                    container.sortableInstance.destroy();
                    //log('销毁旧的Sortable实例');
                }
                
                // 创建新的Sortable实例
                container.sortableInstance = Sortable.create(container, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    handle: '.upload-image-div',
                    filter: '.upload-image-elem',
                    onStart: function(evt) {
                        //log('开始拖拽，索引: ' + evt.oldIndex);
                    },
                    onEnd: function(evt) {
                        //log('拖拽结束，从 ' + evt.oldIndex + ' 到 ' + evt.newIndex);
                        updateMainImage();
                        updateImageOrder();
                        updateImageControls();
                    }
                });
                
                //log('Sortable初始化成功');
                return true;
            } else {
                //log('Sortable初始化失败 - 容器: ' + !!container + ', Sortable: ' + (typeof Sortable));
                return false;
            }
        }
        
        // 更新主图函数
        function updateMainImage() {
            var firstImage = $('#goodsImageContainer .upload-image-div:first input[name="goods_image[]"]').val();
            //log('主图更新为: ' + firstImage);
        }
        
        // 更新图片序号
        function updateImageOrder() {
            $('#goodsImageContainer .upload-image-div').each(function(index) {
                $(this).attr('data-order', index + 1);
            });
            //log('图片序号已更新');
        }
        
        // 更新图片控制按钮
        function updateImageControls() {
            var count = 0;
            $('#goodsImageContainer .upload-image-div').each(function(index) {
                var $this = $(this);
                var controls = $this.find('.image-controls');
                
                if (controls.length === 0) {
                    controls = $('<div class="image-controls"></div>');
                    $this.append(controls);
                    //log('为图片' + (index + 1) + '创建控制按钮容器');
                }
                
                if (index === 0) {
                    controls.find('.btn-set-main').remove();
                } else {
                    if (controls.find('.btn-set-main').length === 0) {
                        controls.append('<button type="button" class="btn-set-main" title="设为主图">主</button>');
                        count++;
                    }
                }
            });
            //log('更新控制按钮完成，添加了 ' + count + ' 个主图按钮');
        }
        
        // 监听设置主图按钮
        $(document).on('click', '#goodsImageContainer .btn-set-main', function(e){
            e.preventDefault();
            e.stopPropagation();
            
            //log('点击了设为主图按钮');
            
            var currentDiv = $(this).closest('.upload-image-div');
            var container = $('#goodsImageContainer .like-upload-image');
            
            // 将当前图片移到第一位
            currentDiv.prependTo(container);
            
            // 更新主图和序号
            updateMainImage();
            updateImageOrder();
            updateImageControls();
            
            //log('设为主图完成');
        });
        
        // 删除图片事件
        $(document).on('click', '.del-upload-btn', function() {
            $(this).parent().remove();
            //log('删除了一张图片');
            updateMainImage();
            updateImageOrder();
            updateImageControls();
        });
        
        // 添加测试图片
        function addTestImage() {
            var imageUrl = 'https://via.placeholder.com/300x300/96CEB4/FFFFFF?text=图片' + testImageIndex;
            var template = '<div class="upload-image-div">' +
                          '<img src="' + imageUrl + '" alt="img" class="preview-image" data-src="' + imageUrl + '" title="点击放大查看">' +
                          '<input type="hidden" name="goods_image[]" value="' + imageUrl + '">' +
                          '<div class="del-upload-btn">×</div>' +
                          '<div class="image-controls"></div>' +
                          '</div>';
            
            $('.upload-image-elem').before(template);
            testImageIndex++;
            
            //log('添加了测试图片: ' + imageUrl);
            
            setTimeout(function() {
                updateImageOrder();
                updateImageControls();
            }, 100);
        }
        
        // 检查状态
        function checkStatus() {
            var imageCount = $('#goodsImageContainer .upload-image-div').length;
            var controlsCount = $('#goodsImageContainer .image-controls').length;
            var buttonsCount = $('#goodsImageContainer .btn-set-main').length;
            var sortableExists = !!document.querySelector('#goodsImageContainer .like-upload-image').sortableInstance;
            
            var status = '图片数量: ' + imageCount + 
                        ', 控制容器: ' + controlsCount + 
                        ', 主图按钮: ' + buttonsCount + 
                        ', 拖拽功能: ' + (sortableExists ? '已启用' : '未启用');
            
            $('#status').text(status);
            //log('状态检查: ' + status);
        }
        
        // 重新初始化
        function reinitialize() {
            //log('开始重新初始化...');
            updateImageOrder();
            updateImageControls();
            initImageSortable();
            checkStatus();
            //log('重新初始化完成');
        }
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            //log('页面加载完成，开始初始化');
            setTimeout(function() {
                updateImageOrder();
                updateImageControls();
                initImageSortable();
                checkStatus();
                //log('初始化完成');
            }, 500);
        });
    </script>
</body>
</html>
