<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Iotvideo\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeForwardRule请求参数结构体
 *
 * @method string getProductID() 获取产品ID
 * @method void setProductID(string $ProductID) 设置产品ID
 * @method string getSkey() 获取控制台Skey
 * @method void setSkey(string $Skey) 设置控制台Skey
 * @method integer getQueueType() 获取队列类型，0：CMQ，1：Ckafka
 * @method void setQueueType(integer $QueueType) 设置队列类型，0：CMQ，1：Ckafka
 * @method string getConsecretid() 获取临时密钥
 * @method void setConsecretid(string $Consecretid) 设置临时密钥
 */
class DescribeForwardRuleRequest extends AbstractModel
{
    /**
     * @var string 产品ID
     */
    public $ProductID;

    /**
     * @var string 控制台Skey
     */
    public $Skey;

    /**
     * @var integer 队列类型，0：CMQ，1：Ckafka
     */
    public $QueueType;

    /**
     * @var string 临时密钥
     */
    public $Consecretid;

    /**
     * @param string $ProductID 产品ID
     * @param string $Skey 控制台Skey
     * @param integer $QueueType 队列类型，0：CMQ，1：Ckafka
     * @param string $Consecretid 临时密钥
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ProductID",$param) and $param["ProductID"] !== null) {
            $this->ProductID = $param["ProductID"];
        }

        if (array_key_exists("Skey",$param) and $param["Skey"] !== null) {
            $this->Skey = $param["Skey"];
        }

        if (array_key_exists("QueueType",$param) and $param["QueueType"] !== null) {
            $this->QueueType = $param["QueueType"];
        }

        if (array_key_exists("Consecretid",$param) and $param["Consecretid"] !== null) {
            $this->Consecretid = $param["Consecretid"];
        }
    }
}
