<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Oceanus\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * StopJobs请求参数结构体
 *
 * @method array getStopJobDescriptions() 获取批量停止作业的描述信息
 * @method void setStopJobDescriptions(array $StopJobDescriptions) 设置批量停止作业的描述信息
 */
class StopJobsRequest extends AbstractModel
{
    /**
     * @var array 批量停止作业的描述信息
     */
    public $StopJobDescriptions;

    /**
     * @param array $StopJobDescriptions 批量停止作业的描述信息
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("StopJobDescriptions",$param) and $param["StopJobDescriptions"] !== null) {
            $this->StopJobDescriptions = [];
            foreach ($param["StopJobDescriptions"] as $key => $value){
                $obj = new StopJobDescription();
                $obj->deserialize($value);
                array_push($this->StopJobDescriptions, $obj);
            }
        }
    }
}
