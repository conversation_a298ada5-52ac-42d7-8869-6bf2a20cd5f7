<?php

namespace app\shop\controller;

use app\common\basics\ShopBase;
use app\shop\logic\ShopQualificationLogic;
use app\common\server\JsonServer;

/**
 * 商家资质管理
 * Class ShopQualification
 * @package app\shop\controller
 */
class ShopQualification extends ShopBase
{
    /**
     * 资质列表
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            $result = ShopQualificationLogic::lists($this->shop_id, $get);
            return JsonServer::success('获取成功', $result);
        }

        return view();
    }

    /**
     * 资质详情
     */
    public function detail()
    {
        $id = $this->request->param('id');
        $detail = ShopQualificationLogic::detail($id, $this->shop_id);
        
        if (!$detail) {
            return JsonServer::error('资质不存在');
        }
        
        return JsonServer::success('获取成功', $detail);
    }

    /**
     * 上传资质页面
     */
    public function upload()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();
            
            // 验证必要字段
            if (empty($post['qualification_id'])) {
                return JsonServer::error('请选择资质类型');
            }
            
            if (empty($post['document_path'])) {
                return JsonServer::error('请上传资质文档');
            }
            
            if (empty($post['document_name'])) {
                return JsonServer::error('文档名称不能为空');
            }
            
            try {
                $result = ShopQualificationLogic::upload($this->shop_id, $post);
                if ($result) {
                    return JsonServer::success('上传成功');
                } else {
                    return JsonServer::error('上传失败');
                }
            } catch (\Exception $e) {
                return JsonServer::error($e->getMessage());
            }
        }
        
        // 获取可用资质列表
        $qualifications = ShopQualificationLogic::getAvailableQualifications();
        \think\facade\View::assign('qualifications', $qualifications);

        return \think\facade\View::fetch();
    }

    /**
     * 删除资质
     */
    public function delete()
    {
        $id = $this->request->post('id');

        if (empty($id)) {
            return JsonServer::error('参数错误');
        }

        try {
            $result = ShopQualificationLogic::delete($id, $this->shop_id);

            if ($result) {
                return JsonServer::success('删除成功');
            } else {
                return JsonServer::error('删除失败');
            }
        } catch (\Exception $e) {
            return JsonServer::error($e->getMessage());
        }
    }

    /**
     * 检查分类资质要求（AJAX接口）
     */
    public function checkCategoryQualifications()
    {
        $categoryId = $this->request->param('category_id');
        
        if (empty($categoryId)) {
            return JsonServer::error('分类ID不能为空');
        }
        
        $result = ShopQualificationLogic::checkCategoryQualifications($this->shop_id, $categoryId);
        
        return JsonServer::success('检查完成', $result);
    }

    /**
     * 快速上传资质（用于商品发布时的弹窗）
     */
    public function quickUpload()
    {
        if ($this->request->isPost()) {
            $post = $this->request->post();
            
            // 验证必要字段
            if (empty($post['qualification_id'])) {
                return JsonServer::error('请选择资质类型');
            }
            
            if (empty($post['document_path'])) {
                return JsonServer::error('请上传资质文档');
            }
            
            if (empty($post['document_name'])) {
                return JsonServer::error('文档名称不能为空');
            }
            
            try {
                $result = ShopQualificationLogic::upload($this->shop_id, $post);
                if ($result) {
                    return JsonServer::success('上传成功');
                } else {
                    return JsonServer::error('上传失败');
                }
            } catch (\Exception $e) {
                return JsonServer::error($e->getMessage());
            }
        }
        
        $qualificationId = $this->request->param('qualification_id');
        $qualificationName = $this->request->param('qualification_name', '');

        // 获取资质详细信息
        $qualification = null;
        if ($qualificationId) {
            $qualification = \app\common\model\goods\Qualification::where(['id' => $qualificationId, 'status' => 1, 'del' => 0])
                ->field('id,name,description,document_path,document_name,ex_img')
                ->find();
        }

        \think\facade\View::assign([
            'qualification_id' => $qualificationId,
            'qualification_name' => $qualificationName,
            'qualification' => $qualification
        ]);

        return \think\facade\View::fetch();
    }

    /**
     * 获取可用资质列表
     */
    public function getQualifications()
    {
        $qualifications = ShopQualificationLogic::getAvailableQualifications();
        return JsonServer::success('获取成功', $qualifications);
    }
}
