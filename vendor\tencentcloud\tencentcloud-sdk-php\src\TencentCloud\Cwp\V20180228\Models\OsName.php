<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cwp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 操作系统名称
 *
 * @method string getName() 获取系统名称
 * @method void setName(string $Name) 设置系统名称
 * @method integer getMachineOSType() 获取操作系统类型枚举值
 * @method void setMachineOSType(integer $MachineOSType) 设置操作系统类型枚举值
 */
class OsName extends AbstractModel
{
    /**
     * @var string 系统名称
     */
    public $Name;

    /**
     * @var integer 操作系统类型枚举值
     */
    public $MachineOSType;

    /**
     * @param string $Name 系统名称
     * @param integer $MachineOSType 操作系统类型枚举值
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Name",$param) and $param["Name"] !== null) {
            $this->Name = $param["Name"];
        }

        if (array_key_exists("MachineOSType",$param) and $param["MachineOSType"] !== null) {
            $this->MachineOSType = $param["MachineOSType"];
        }
    }
}
