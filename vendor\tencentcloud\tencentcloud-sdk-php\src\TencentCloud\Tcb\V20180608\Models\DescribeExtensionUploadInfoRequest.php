<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tcb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeExtensionUploadInfo请求参数结构体
 *
 * @method array getExtensionFiles() 获取待上传的文件
 * @method void setExtensionFiles(array $ExtensionFiles) 设置待上传的文件
 */
class DescribeExtensionUploadInfoRequest extends AbstractModel
{
    /**
     * @var array 待上传的文件
     */
    public $ExtensionFiles;

    /**
     * @param array $ExtensionFiles 待上传的文件
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ExtensionFiles",$param) and $param["ExtensionFiles"] !== null) {
            $this->ExtensionFiles = [];
            foreach ($param["ExtensionFiles"] as $key => $value){
                $obj = new ExtensionFile();
                $obj->deserialize($value);
                array_push($this->ExtensionFiles, $obj);
            }
        }
    }
}
