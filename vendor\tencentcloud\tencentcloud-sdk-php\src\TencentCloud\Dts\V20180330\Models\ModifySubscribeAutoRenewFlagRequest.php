<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dts\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ModifySubscribeAutoRenewFlag请求参数结构体
 *
 * @method string getSubscribeId() 获取订阅实例ID，例如：subs-8uey736k
 * @method void setSubscribeId(string $SubscribeId) 设置订阅实例ID，例如：subs-8uey736k
 * @method integer getAutoRenewFlag() 获取自动续费标识。1-自动续费，0-不自动续费
 * @method void setAutoRenewFlag(integer $AutoRenewFlag) 设置自动续费标识。1-自动续费，0-不自动续费
 */
class ModifySubscribeAutoRenewFlagRequest extends AbstractModel
{
    /**
     * @var string 订阅实例ID，例如：subs-8uey736k
     */
    public $SubscribeId;

    /**
     * @var integer 自动续费标识。1-自动续费，0-不自动续费
     */
    public $AutoRenewFlag;

    /**
     * @param string $SubscribeId 订阅实例ID，例如：subs-8uey736k
     * @param integer $AutoRenewFlag 自动续费标识。1-自动续费，0-不自动续费
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SubscribeId",$param) and $param["SubscribeId"] !== null) {
            $this->SubscribeId = $param["SubscribeId"];
        }

        if (array_key_exists("AutoRenewFlag",$param) and $param["AutoRenewFlag"] !== null) {
            $this->AutoRenewFlag = $param["AutoRenewFlag"];
        }
    }
}
