<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Mps\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ParseLiveStreamProcessNotification请求参数结构体
 *
 * @method string getContent() 获取从 CMQ 获取到的直播流事件通知内容。
 * @method void setContent(string $Content) 设置从 CMQ 获取到的直播流事件通知内容。
 */
class ParseLiveStreamProcessNotificationRequest extends AbstractModel
{
    /**
     * @var string 从 CMQ 获取到的直播流事件通知内容。
     */
    public $Content;

    /**
     * @param string $Content 从 CMQ 获取到的直播流事件通知内容。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Content",$param) and $param["Content"] !== null) {
            $this->Content = $param["Content"];
        }
    }
}
