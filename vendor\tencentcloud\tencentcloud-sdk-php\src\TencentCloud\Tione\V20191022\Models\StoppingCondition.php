<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tione\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 终止条件
 *
 * @method integer getMaxRuntimeInSeconds() 获取最长运行运行时间（秒）
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setMaxRuntimeInSeconds(integer $MaxRuntimeInSeconds) 设置最长运行运行时间（秒）
注意：此字段可能返回 null，表示取不到有效值。
 * @method integer getMaxWaitTimeInSeconds() 获取最长等待运行时间（秒）
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setMaxWaitTimeInSeconds(integer $MaxWaitTimeInSeconds) 设置最长等待运行时间（秒）
注意：此字段可能返回 null，表示取不到有效值。
 */
class StoppingCondition extends AbstractModel
{
    /**
     * @var integer 最长运行运行时间（秒）
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $MaxRuntimeInSeconds;

    /**
     * @var integer 最长等待运行时间（秒）
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $MaxWaitTimeInSeconds;

    /**
     * @param integer $MaxRuntimeInSeconds 最长运行运行时间（秒）
注意：此字段可能返回 null，表示取不到有效值。
     * @param integer $MaxWaitTimeInSeconds 最长等待运行时间（秒）
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("MaxRuntimeInSeconds",$param) and $param["MaxRuntimeInSeconds"] !== null) {
            $this->MaxRuntimeInSeconds = $param["MaxRuntimeInSeconds"];
        }

        if (array_key_exists("MaxWaitTimeInSeconds",$param) and $param["MaxWaitTimeInSeconds"] !== null) {
            $this->MaxWaitTimeInSeconds = $param["MaxWaitTimeInSeconds"];
        }
    }
}
