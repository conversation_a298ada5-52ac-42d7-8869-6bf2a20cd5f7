<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tia\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ListModels请求参数结构体
 *
 * @method string getCluster() 获取部署模型的集群， `集群模式` 必填
 * @method void setCluster(string $Cluster) 设置部署模型的集群， `集群模式` 必填
 * @method integer getLimit() 获取分页参数，返回数量上限
 * @method void setLimit(integer $Limit) 设置分页参数，返回数量上限
 * @method integer getOffset() 获取分页参数，分页起始位置
 * @method void setOffset(integer $Offset) 设置分页参数，分页起始位置
 * @method string getServType() 获取部署类型，取值 `serverless` 即为 `无服务器模式`，否则为 `集群模式`。
 * @method void setServType(string $ServType) 设置部署类型，取值 `serverless` 即为 `无服务器模式`，否则为 `集群模式`。
 */
class ListModelsRequest extends AbstractModel
{
    /**
     * @var string 部署模型的集群， `集群模式` 必填
     */
    public $Cluster;

    /**
     * @var integer 分页参数，返回数量上限
     */
    public $Limit;

    /**
     * @var integer 分页参数，分页起始位置
     */
    public $Offset;

    /**
     * @var string 部署类型，取值 `serverless` 即为 `无服务器模式`，否则为 `集群模式`。
     */
    public $ServType;

    /**
     * @param string $Cluster 部署模型的集群， `集群模式` 必填
     * @param integer $Limit 分页参数，返回数量上限
     * @param integer $Offset 分页参数，分页起始位置
     * @param string $ServType 部署类型，取值 `serverless` 即为 `无服务器模式`，否则为 `集群模式`。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Cluster",$param) and $param["Cluster"] !== null) {
            $this->Cluster = $param["Cluster"];
        }

        if (array_key_exists("Limit",$param) and $param["Limit"] !== null) {
            $this->Limit = $param["Limit"];
        }

        if (array_key_exists("Offset",$param) and $param["Offset"] !== null) {
            $this->Offset = $param["Offset"];
        }

        if (array_key_exists("ServType",$param) and $param["ServType"] !== null) {
            $this->ServType = $param["ServType"];
        }
    }
}
