<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tci\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 人脸表情统计结果
 *
 * @method string getPersonId() 获取人员唯一标识符
 * @method void setPersonId(string $PersonId) 设置人员唯一标识符
 * @method array getExpressRatio() 获取表情统计结果
 * @method void setExpressRatio(array $ExpressRatio) 设置表情统计结果
 */
class FaceExpressStatistic extends AbstractModel
{
    /**
     * @var string 人员唯一标识符
     */
    public $PersonId;

    /**
     * @var array 表情统计结果
     */
    public $ExpressRatio;

    /**
     * @param string $PersonId 人员唯一标识符
     * @param array $ExpressRatio 表情统计结果
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("PersonId",$param) and $param["PersonId"] !== null) {
            $this->PersonId = $param["PersonId"];
        }

        if (array_key_exists("ExpressRatio",$param) and $param["ExpressRatio"] !== null) {
            $this->ExpressRatio = [];
            foreach ($param["ExpressRatio"] as $key => $value){
                $obj = new ExpressRatioStatistic();
                $obj->deserialize($value);
                array_push($this->ExpressRatio, $obj);
            }
        }
    }
}
