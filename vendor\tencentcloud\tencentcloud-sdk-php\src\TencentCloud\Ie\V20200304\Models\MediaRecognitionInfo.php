<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ie\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 媒体识别任务参数
 *
 * @method FrameTagRec getFrameTagRec() 获取帧标签识别
 * @method void setFrameTagRec(FrameTagRec $FrameTagRec) 设置帧标签识别
 * @method SubtitleRec getSubtitleRec() 获取语音字幕识别
 * @method void setSubtitleRec(SubtitleRec $SubtitleRec) 设置语音字幕识别
 */
class MediaRecognitionInfo extends AbstractModel
{
    /**
     * @var FrameTagRec 帧标签识别
     */
    public $FrameTagRec;

    /**
     * @var SubtitleRec 语音字幕识别
     */
    public $SubtitleRec;

    /**
     * @param FrameTagRec $FrameTagRec 帧标签识别
     * @param SubtitleRec $SubtitleRec 语音字幕识别
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("FrameTagRec",$param) and $param["FrameTagRec"] !== null) {
            $this->FrameTagRec = new FrameTagRec();
            $this->FrameTagRec->deserialize($param["FrameTagRec"]);
        }

        if (array_key_exists("SubtitleRec",$param) and $param["SubtitleRec"] !== null) {
            $this->SubtitleRec = new SubtitleRec();
            $this->SubtitleRec->deserialize($param["SubtitleRec"]);
        }
    }
}
