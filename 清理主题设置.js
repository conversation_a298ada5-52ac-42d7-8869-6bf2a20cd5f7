/**
 * 清理浏览器中残留的主题设置
 * 在浏览器控制台中运行此脚本
 */

// 清理localStorage中的主题设置
localStorage.removeItem('layuiadmin-theme');

// 移除body和html上的主题属性
document.body.removeAttribute('data-theme');
document.documentElement.removeAttribute('data-theme');

// 清理所有iframe中的主题属性
const iframes = document.querySelectorAll('.layadmin-iframe');
iframes.forEach(iframe => {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    if (iframeDoc && iframeDoc.body) {
      iframeDoc.body.removeAttribute('data-theme');
      iframeDoc.documentElement.removeAttribute('data-theme');
    }
  } catch (e) {
    // 忽略跨域错误
  }
});

console.log('主题设置已清理完成');
