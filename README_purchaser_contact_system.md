# 采购商联系记录系统实现文档

## 概述
本系统实现了采购商联系记录功能，包括记录用户联系采购商的行为、查看联系记录、统计分析等功能。

## 功能特性
1. **联系记录**：记录用户点击联系采购商的行为
2. **防重复联系**：1分钟内防止重复联系同一采购商
3. **多端支持**：支持web、app、小程序等多种来源
4. **统计分析**：提供详细的联系统计数据
5. **权限控制**：商家只能查看自己发布的采购信息的联系记录

## 数据库表结构

### 联系记录表 (ls_purchaser_contact_record)
```sql
CREATE TABLE `ls_purchaser_contact_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '联系用户ID',
  `purchaser_id` int(11) NOT NULL COMMENT '被联系的采购商ID（对应community_article表的id）',
  `contact_time` int(11) NOT NULL COMMENT '联系时间戳',
  `ip` varchar(45) DEFAULT NULL COMMENT '联系时的IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理信息',
  `source` varchar(50) DEFAULT 'web' COMMENT '联系来源：web-网页，app-应用，mini-小程序',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_purchaser_id` (`purchaser_id`),
  KEY `idx_contact_time` (`contact_time`),
  KEY `idx_user_purchaser` (`user_id`, `purchaser_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购商联系记录表';
```

## API接口

### 前端用户接口 (app/api/controller/PurchaserContact.php)

#### 1. 记录联系采购商
- **接口地址**：`POST /api/purchaser_contact/record_contact`
- **参数**：
  - `purchaser_id` (必填): 采购商ID
  - `source` (可选): 来源标识 (web/app/mini)
- **返回**：联系记录信息

#### 2. 获取用户联系记录
- **接口地址**：`GET /api/purchaser_contact/get_contact_records`
- **参数**：
  - `page_no`: 页码
  - `page_size`: 每页数量
  - `start_time`: 开始时间
  - `end_time`: 结束时间
- **返回**：联系记录列表

#### 3. 获取联系统计
- **接口地址**：`GET /api/purchaser_contact/get_contact_stats`
- **返回**：统计数据

### 商家后台接口 (app/shopapi/controller/Purchaser.php)

#### 1. 获取商家联系记录
- **接口地址**：`GET /shopapi/purchaser/contact_records`
- **参数**：分页和筛选参数
- **返回**：商家相关的联系记录

#### 2. 获取商家联系统计
- **接口地址**：`GET /shopapi/purchaser/contact_stats`
- **返回**：商家的联系统计数据

## 商家后台管理

### 控制器 (app/shop/controller/PurchaserContact.php)
提供商家后台的联系记录管理功能：
- 联系记录列表
- 记录详情查看
- 统计分析
- 数据导出

### 菜单配置
在商家后台添加"客户管理"菜单，包含：
- 联系记录查看
- 联系统计分析
- 数据导出功能

## 前端菜单配置

### 个人中心菜单
在用户个人中心添加：
- 我的联系记录
- 联系统计

## 安装步骤

### 1. 执行数据库脚本
```bash
# 创建联系记录表
mysql -u用户名 -p数据库名 < database/purchaser_contact_record.sql

# 添加商家后台菜单
mysql -u用户名 -p数据库名 < database/shop_menu_purchaser_contact.sql

# 添加前端菜单
mysql -u用户名 -p数据库名 < database/purchaser_menu.sql
```

### 2. 部署代码文件
确保以下文件已正确部署：
- `app/api/controller/PurchaserContact.php`
- `app/api/logic/PurchaserContactLogic.php`
- `app/common/model/PurchaserContactRecord.php`
- `app/shop/controller/PurchaserContact.php`
- `app/shop/logic/PurchaserContactLogic.php`

### 3. 配置路由
确保API路由正确配置，可以访问相关接口。

## 使用示例

### 前端调用示例
```javascript
// 记录联系采购商
fetch('/api/purchaser_contact/record_contact', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        purchaser_id: 123,
        source: 'web'
    })
});

// 获取联系记录
fetch('/api/purchaser_contact/get_contact_records?page_no=1&page_size=10');
```

## 注意事项

1. **权限控制**：所有接口都需要用户登录
2. **防刷机制**：1分钟内不能重复联系同一采购商
3. **数据关联**：联系记录通过community_article表关联到具体的商家
4. **性能优化**：建议对高频查询字段添加索引
5. **数据清理**：建议定期清理过期的联系记录数据

## 视图文件

### 商家后台视图文件
- `app/shop/view/purchaser_contact/lists.html` - 联系记录列表页面
- `app/shop/view/purchaser_contact/detail.html` - 联系记录详情页面
- `app/shop/view/purchaser_contact/stats.html` - 联系统计页面

### 视图文件特性
1. **响应式设计**：适配不同屏幕尺寸
2. **搜索筛选**：支持按来源、时间、用户等条件筛选
3. **数据展示**：清晰展示用户信息、联系时间、来源等
4. **交互功能**：支持查看详情、统计分析、数据导出
5. **美观界面**：使用LayUI框架，界面美观统一

## 测试建议

### 1. 功能测试
```bash
# 测试记录联系接口
curl -X POST "http://your-domain/api/purchaser_contact/record_contact" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"purchaser_id": 1, "source": "web"}'

# 测试获取联系记录接口
curl -X GET "http://your-domain/api/purchaser_contact/get_contact_records?page_no=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 商家后台测试
1. 登录商家后台
2. 访问"客户管理" -> "联系记录"菜单
3. 测试搜索、筛选、详情查看功能
4. 测试统计页面数据展示

### 3. 性能测试
- 测试大量数据下的查询性能
- 测试并发联系记录的处理
- 验证防重复联系机制

## 扩展功能

后续可以考虑添加：
1. 联系记录的消息推送
2. 联系热度分析
3. 采购商推荐算法
4. 联系记录的导出功能完善（Excel/CSV）
5. 更详细的统计报表
6. 联系记录的批量操作
7. 联系效果分析（转化率统计）
8. 自动回复功能
