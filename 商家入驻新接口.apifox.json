{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "商家入驻新接口文档", "description": "基于新的商家入驻逻辑设计的API接口文档，支持0元入驻、商家会员、实力厂商三种入驻方式。", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "商家入驻管理", "id": 55359880, "auth": {}, "securityScheme": {}, "parentId": 46973025, "serverId": "", "description": "商家入驻相关接口", "items": [{"name": "获取入驻选项配置", "api": {"id": "288483560", "method": "get", "path": "/api/shop_entry/getEntryOptions", "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "获取成功"}, "data": {"type": "object", "properties": {"free_entry": {"type": "object", "properties": {"name": {"type": "string", "example": "0元入驻"}, "price": {"type": "number", "example": 0}, "description": {"type": "string", "example": "免费入驻，立即开店"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["基础店铺功能", "商品发布", "订单管理"]}}}, "member_entry": {"type": "object", "properties": {"name": {"type": "string", "example": "商家会员"}, "price": {"type": "number", "example": 0.01}, "description": {"type": "string", "example": "付费会员，享受更多权益"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["所有基础功能", "优先展示", "营销工具", "数据分析"]}}}, "premium_entry": {"type": "object", "properties": {"name": {"type": "string", "example": "实力厂商"}, "price": {"type": "number", "example": 0.01}, "description": {"type": "string", "example": "实力认证，品牌保障"}, "features": {"type": "array", "items": {"type": "string"}, "example": ["所有会员功能", "实力认证标识", "品牌保障", "专属客服", "优先推荐"]}}}}}}}}]}}, {"name": "0元入驻申请", "api": {"id": "*********", "method": "post", "path": "/api/shop_entry/freeEntry", "parameters": {"body": {"type": "object", "properties": {"cid": {"type": "integer", "description": "商家分类ID", "example": 1}, "name": {"type": "string", "description": "商家名称", "example": "测试商家"}, "nickname": {"type": "string", "description": "商家简称", "example": "测试店"}, "mobile": {"type": "string", "description": "手机号", "example": "***********"}, "account": {"type": "string", "description": "登录账号", "example": "testshop"}, "password": {"type": "string", "description": "登录密码", "example": "123456"}, "license": {"type": "array", "description": "营业执照图片", "items": {"type": "string"}, "example": ["license1.jpg", "license2.jpg"]}}, "required": ["cid", "name", "nickname", "mobile", "account", "password", "license"]}}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "入驻成功"}, "data": {"type": "object", "properties": {"shop_id": {"type": "integer", "description": "商家ID", "example": 123}, "tier_level": {"type": "integer", "description": "商家等级", "example": 0}, "message": {"type": "string", "description": "成功信息", "example": "0元入驻成功"}}}}}}]}}, {"name": "商家会员支付", "api": {"id": "*********", "method": "post", "path": "/api/shop_entry/memberPay", "parameters": {"body": {"type": "object", "properties": {"pay_way": {"type": "integer", "description": "支付方式：1微信支付，2支付宝", "example": 1}, "from": {"type": "integer", "description": "客户端类型：1小程序，2公众号，3H5，4PC，5APP", "example": 1}}, "required": ["pay_way"]}}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "支付订单创建成功"}, "data": {"type": "object", "description": "支付参数，根据支付方式和客户端类型返回不同格式"}}}}]}}, {"name": "实力厂商支付", "api": {"id": "288483563", "method": "post", "path": "/api/shop_entry/premiumPay", "parameters": {"body": {"type": "object", "properties": {"pay_way": {"type": "integer", "description": "支付方式：1微信支付，2支付宝", "example": 1}, "from": {"type": "integer", "description": "客户端类型：1小程序，2公众号，3H5，4PC，5APP", "example": 1}}, "required": ["pay_way"]}}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "支付订单创建成功"}, "data": {"type": "object", "description": "支付参数，根据支付方式和客户端类型返回不同格式"}}}}]}}, {"name": "商家会员入驻信息填写", "api": {"id": "*********", "method": "post", "path": "/api/shop_entry/memberApply", "parameters": {"body": {"type": "object", "properties": {"cid": {"type": "integer", "description": "商家分类ID", "example": 1}, "name": {"type": "string", "description": "商家名称", "example": "测试商家"}, "nickname": {"type": "string", "description": "商家简称", "example": "测试店"}, "mobile": {"type": "string", "description": "手机号", "example": "***********"}, "account": {"type": "string", "description": "登录账号", "example": "testshop"}, "password": {"type": "string", "description": "登录密码", "example": "123456"}, "license": {"type": "array", "description": "营业执照图片", "items": {"type": "string"}, "example": ["license1.jpg", "license2.jpg"]}}, "required": ["cid", "name", "nickname", "mobile", "account", "password", "license"]}}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "入驻成功"}, "data": {"type": "object", "properties": {"shop_id": {"type": "integer", "description": "商家ID", "example": 123}, "tier_level": {"type": "integer", "description": "商家等级", "example": 1}, "message": {"type": "string", "description": "成功信息", "example": "商家会员入驻成功"}}}}}}]}}, {"name": "实力厂商入驻信息填写", "api": {"id": "*********", "method": "post", "path": "/api/shop_entry/premiumApply", "parameters": {"body": {"type": "object", "properties": {"cid": {"type": "integer", "description": "商家分类ID", "example": 1}, "name": {"type": "string", "description": "商家名称", "example": "测试商家"}, "nickname": {"type": "string", "description": "商家简称", "example": "测试店"}, "mobile": {"type": "string", "description": "手机号", "example": "***********"}, "account": {"type": "string", "description": "登录账号", "example": "testshop"}, "password": {"type": "string", "description": "登录密码", "example": "123456"}, "license": {"type": "array", "description": "营业执照图片", "items": {"type": "string"}, "example": ["license1.jpg", "license2.jpg"]}}, "required": ["cid", "name", "nickname", "mobile", "account", "password", "license"]}}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "入驻成功"}, "data": {"type": "object", "properties": {"shop_id": {"type": "integer", "description": "商家ID", "example": 123}, "tier_level": {"type": "integer", "description": "商家等级", "example": 2}, "message": {"type": "string", "description": "成功信息", "example": "实力厂商入驻成功"}}}}}}]}}, {"name": "获取用户入驻状态", "api": {"id": "*********", "method": "get", "path": "/api/shop_entry/getEntryStatus", "parameters": {}, "responses": [{"code": 200, "name": "成功", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：1成功，0失败", "example": 1}, "msg": {"type": "string", "description": "提示信息", "example": "获取成功"}, "data": {"type": "object", "properties": {"status": {"type": "string", "description": "状态：not_started未开始，unpaid未支付，paid_pending_info已支付待填信息，completed已完成", "example": "paid_pending_info"}, "shop_id": {"type": "integer", "description": "商家ID（已完成时返回）", "example": 123}, "order_sn": {"type": "string", "description": "订单号（未支付时返回）", "example": "202506061800000001"}, "feetype": {"type": "integer", "description": "费用类型：0商家会员，1实力厂商", "example": 0}, "tier_level": {"type": "integer", "description": "商家等级：0-0元入驻，1-商家会员，2-实力厂商", "example": 1}, "tier_name": {"type": "string", "description": "等级名称", "example": "商家会员"}, "amount": {"type": "number", "description": "金额", "example": 0.01}, "payment_date": {"type": "integer", "description": "支付时间戳（已支付时返回）", "example": 1733472000}, "message": {"type": "string", "description": "状态描述", "example": "已支付，请填写入驻信息"}}}}}}]}}]}], "commonParameters": {"parameters": {"header": [{"name": "token", "defaultEnable": true, "type": "string", "id": "EhhmXmBW8X", "description": "登录令牌", "required": true}]}}, "projectSetting": {"id": "5618395", "auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}]}}