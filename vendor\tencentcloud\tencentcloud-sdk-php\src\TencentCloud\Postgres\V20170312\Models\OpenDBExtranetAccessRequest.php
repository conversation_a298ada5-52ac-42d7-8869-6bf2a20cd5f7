<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Postgres\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * OpenDBExtranetAccess请求参数结构体
 *
 * @method string getDBInstanceId() 获取实例ID，形如postgres-hez4fh0v
 * @method void setDBInstanceId(string $DBInstanceId) 设置实例ID，形如postgres-hez4fh0v
 * @method integer getIsIpv6() 获取是否开通Ipv6外网，1：是，0：否
 * @method void setIsIpv6(integer $IsIpv6) 设置是否开通Ipv6外网，1：是，0：否
 */
class OpenDBExtranetAccessRequest extends AbstractModel
{
    /**
     * @var string 实例ID，形如postgres-hez4fh0v
     */
    public $DBInstanceId;

    /**
     * @var integer 是否开通Ipv6外网，1：是，0：否
     */
    public $IsIpv6;

    /**
     * @param string $DBInstanceId 实例ID，形如postgres-hez4fh0v
     * @param integer $IsIpv6 是否开通Ipv6外网，1：是，0：否
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("DBInstanceId",$param) and $param["DBInstanceId"] !== null) {
            $this->DBInstanceId = $param["DBInstanceId"];
        }

        if (array_key_exists("IsIpv6",$param) and $param["IsIpv6"] !== null) {
            $this->IsIpv6 = $param["IsIpv6"];
        }
    }
}
