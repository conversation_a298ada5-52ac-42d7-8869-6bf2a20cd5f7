<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ocr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 候选字符集(包含候选字Character以及置信度Confidence)
 *
 * @method array getCandWords() 获取候选字符集的单词信息（包括单词Character和单词置信度confidence）
 * @method void setCandWords(array $CandWords) 设置候选字符集的单词信息（包括单词Character和单词置信度confidence）
 */
class CandWord extends AbstractModel
{
    /**
     * @var array 候选字符集的单词信息（包括单词Character和单词置信度confidence）
     */
    public $CandWords;

    /**
     * @param array $CandWords 候选字符集的单词信息（包括单词Character和单词置信度confidence）
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("CandWords",$param) and $param["CandWords"] !== null) {
            $this->CandWords = [];
            foreach ($param["CandWords"] as $key => $value){
                $obj = new Words();
                $obj->deserialize($value);
                array_push($this->CandWords, $obj);
            }
        }
    }
}
