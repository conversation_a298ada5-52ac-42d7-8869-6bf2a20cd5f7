<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ses\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteBlackList请求参数结构体
 *
 * @method array getEmailAddressList() 获取需要清除的黑名单邮箱列表，数组长度至少为1
 * @method void setEmailAddressList(array $EmailAddressList) 设置需要清除的黑名单邮箱列表，数组长度至少为1
 */
class DeleteBlackListRequest extends AbstractModel
{
    /**
     * @var array 需要清除的黑名单邮箱列表，数组长度至少为1
     */
    public $EmailAddressList;

    /**
     * @param array $EmailAddressList 需要清除的黑名单邮箱列表，数组长度至少为1
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("EmailAddressList",$param) and $param["EmailAddressList"] !== null) {
            $this->EmailAddressList = $param["EmailAddressList"];
        }
    }
}
