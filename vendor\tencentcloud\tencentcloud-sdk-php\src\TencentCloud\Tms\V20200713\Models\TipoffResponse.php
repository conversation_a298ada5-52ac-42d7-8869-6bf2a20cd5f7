<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tms\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 举报接口响应数据
 *
 * @method integer getResultCode() 获取举报结果， "0-举报数据提交成功  99-举报数据提交失败"
 * @method void setResultCode(integer $ResultCode) 设置举报结果， "0-举报数据提交成功  99-举报数据提交失败"
 * @method string getResultMsg() 获取结果描述
 * @method void setResultMsg(string $ResultMsg) 设置结果描述
 */
class TipoffResponse extends AbstractModel
{
    /**
     * @var integer 举报结果， "0-举报数据提交成功  99-举报数据提交失败"
     */
    public $ResultCode;

    /**
     * @var string 结果描述
     */
    public $ResultMsg;

    /**
     * @param integer $ResultCode 举报结果， "0-举报数据提交成功  99-举报数据提交失败"
     * @param string $ResultMsg 结果描述
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ResultCode",$param) and $param["ResultCode"] !== null) {
            $this->ResultCode = $param["ResultCode"];
        }

        if (array_key_exists("ResultMsg",$param) and $param["ResultMsg"] !== null) {
            $this->ResultMsg = $param["ResultMsg"];
        }
    }
}
