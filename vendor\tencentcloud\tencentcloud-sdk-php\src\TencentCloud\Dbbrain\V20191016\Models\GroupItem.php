<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dbbrain\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 描述组信息。
 *
 * @method integer getId() 获取组id。
 * @method void setId(integer $Id) 设置组id。
 * @method string getName() 获取组名称。
 * @method void setName(string $Name) 设置组名称。
 * @method integer getMemberCount() 获取组成员数量。
 * @method void setMemberCount(integer $MemberCount) 设置组成员数量。
 */
class GroupItem extends AbstractModel
{
    /**
     * @var integer 组id。
     */
    public $Id;

    /**
     * @var string 组名称。
     */
    public $Name;

    /**
     * @var integer 组成员数量。
     */
    public $MemberCount;

    /**
     * @param integer $Id 组id。
     * @param string $Name 组名称。
     * @param integer $MemberCount 组成员数量。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Id",$param) and $param["Id"] !== null) {
            $this->Id = $param["Id"];
        }

        if (array_key_exists("Name",$param) and $param["Name"] !== null) {
            $this->Name = $param["Name"];
        }

        if (array_key_exists("MemberCount",$param) and $param["MemberCount"] !== null) {
            $this->MemberCount = $param["MemberCount"];
        }
    }
}
