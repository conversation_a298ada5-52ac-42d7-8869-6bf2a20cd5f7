<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品图片预览功能演示</title>
    <link rel="stylesheet" href="public/static/lib/layui/css/layui.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .demo-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .demo-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .demo-content {
            padding: 40px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border-left: 4px solid #667eea;
        }
        
        .feature-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .feature-card ul {
            margin: 0;
            padding-left: 20px;
            color: #666;
        }
        
        .feature-card li {
            margin-bottom: 8px;
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 22px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .demo-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }
        
        .demo-image:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .demo-image.preview-image {
            position: relative;
        }
        
        .demo-image.preview-image::after {
            content: '🔍';
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .demo-image.preview-image:hover::after {
            opacity: 1;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-fixed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-improved {
            background: #cce5ff;
            color: #004085;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e6e6e6;
        }
        
        .comparison-table th {
            background: #667eea;
            color: white;
            font-weight: 500;
        }
        
        .comparison-table tr:last-child td {
            border-bottom: none;
        }
        
        .comparison-table .old {
            color: #dc3545;
        }
        
        .comparison-table .new {
            color: #28a745;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>商品图片预览功能优化</h1>
            <p>解决重复弹窗问题，提供统一的图片预览体验</p>
        </div>
        
        <div class="demo-content">
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔧 问题修复 <span class="status-indicator status-fixed">已修复</span></h3>
                    <ul>
                        <li>解决了点击图片出现两个弹窗的问题</li>
                        <li>消除了多个预览方法之间的冲突</li>
                        <li>防止快速重复点击导致的异常</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>⚡ 性能优化 <span class="status-indicator status-improved">已优化</span></h3>
                    <ul>
                        <li>添加了防重复调用锁定机制</li>
                        <li>实现了100ms防抖处理</li>
                        <li>优化了图片尺寸计算算法</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🎨 用户体验 <span class="status-indicator status-improved">已提升</span></h3>
                    <ul>
                        <li>支持点击图片或遮罩关闭</li>
                        <li>支持ESC键快速关闭</li>
                        <li>自动计算最佳显示尺寸</li>
                        <li>保持图片原始宽高比</li>
                    </ul>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>功能对比</h2>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>功能特性</th>
                            <th>修复前</th>
                            <th>修复后</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>弹窗数量</td>
                            <td class="old">❌ 同时显示2个弹窗</td>
                            <td class="new">✅ 只显示1个弹窗</td>
                        </tr>
                        <tr>
                            <td>重复点击</td>
                            <td class="old">❌ 可能导致多个弹窗</td>
                            <td class="new">✅ 防抖处理，避免重复</td>
                        </tr>
                        <tr>
                            <td>关闭方式</td>
                            <td class="old">⚠️ 仅支持关闭按钮</td>
                            <td class="new">✅ 支持多种关闭方式</td>
                        </tr>
                        <tr>
                            <td>图片尺寸</td>
                            <td class="old">⚠️ 固定尺寸，可能变形</td>
                            <td class="new">✅ 智能计算，保持比例</td>
                        </tr>
                        <tr>
                            <td>代码维护</td>
                            <td class="old">❌ 多处重复代码</td>
                            <td class="new">✅ 统一方法，易于维护</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="demo-section">
                <h2>图片预览演示</h2>
                <p>点击下面的图片体验新的预览功能：</p>
                <div class="image-gallery">
                    <img src="https://picsum.photos/400/300?random=1" 
                         class="demo-image preview-image" 
                         data-src="https://picsum.photos/800/600?random=1" 
                         title="点击放大查看 - 横向图片" />
                    <img src="https://picsum.photos/300/400?random=2" 
                         class="demo-image preview-image" 
                         data-src="https://picsum.photos/600/800?random=2" 
                         title="点击放大查看 - 纵向图片" />
                    <img src="https://picsum.photos/400/400?random=3" 
                         class="demo-image preview-image" 
                         data-src="https://picsum.photos/800/800?random=3" 
                         title="点击放大查看 - 正方形图片" />
                    <img src="https://picsum.photos/500/200?random=4" 
                         class="demo-image preview-image" 
                         data-src="https://picsum.photos/1000/400?random=4" 
                         title="点击放大查看 - 宽屏图片" />
                </div>
                <p style="color: #666; font-size: 14px; margin-top: 20px;">
                    💡 提示：现在点击任何图片都只会显示一个弹窗，支持点击图片、遮罩或按ESC键关闭
                </p>
            </div>
        </div>
    </div>

    <script src="public/static/lib/layui/layui.js"></script>
    <script src="public/static/lib/jquery/jquery.min.js"></script>
    <script src="public/static/admin/js/function.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('图片预览功能演示页面加载完成');
            console.log('like.goodsImagePreview 方法可用:', typeof like.goodsImagePreview === 'function');
        });
    </script>
</body>
</html>
