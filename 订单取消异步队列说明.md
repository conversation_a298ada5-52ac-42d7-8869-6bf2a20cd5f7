# 订单取消异步队列功能说明

## 功能概述

将原来的同步订单取消接口改为异步队列处理，提高接口响应速度，避免因订单取消逻辑复杂导致的接口超时问题。

## 修改内容

### 1. 创建队列任务类
- **文件**: `app/common/job/OrderCancelJob.php`
- **功能**: 处理订单取消的具体逻辑
- **特性**:
  - 支持失败重试（最多3次）
  - 详细的日志记录
  - 异常处理机制

### 2. 修改订单控制器
- **文件**: `app/api/controller/Order.php`
- **方法**: `cancel()`
- **改动**: 将同步调用改为异步队列推送
- **回退机制**: 当队列推送失败时，自动回退到同步处理

### 3. 更新队列配置
- **文件**: `config/queue.php`
- **改动**: 使用项目的Redis配置，确保队列正常工作

## 使用方法

### 1. 启动队列监听
```bash
# 方法一：使用提供的启动脚本
php start_queue.php

# 方法二：直接使用think命令
php think queue:listen --queue=orderCancel --delay=0 --sleep=3 --tries=3

# 方法三：使用work命令（处理完当前任务后退出）
php think queue:work --queue=orderCancel
```

### 2. 测试队列功能
```bash
# 运行测试脚本
php test_queue.php
```

### 3. 生产环境部署
建议使用 supervisor 管理队列进程，确保进程常驻：

```ini
[program:order_cancel_queue]
command=php /path/to/project/think queue:listen --queue=orderCancel
directory=/path/to/project
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/order_cancel_queue.log
```

## 接口变化

### 原接口行为
- 同步处理订单取消
- 处理时间较长
- 可能出现超时

### 新接口行为
- 立即返回成功响应
- 异步处理订单取消
- 提高用户体验

### 响应示例
```json
{
    "code": 1,
    "msg": "订单取消请求已提交，正在处理中...",
    "data": []
}
```

## 监控和日志

### 日志位置
- 应用日志: `runtime/log/`
- 队列任务日志: 搜索 "OrderCancelJob"

### 监控要点
1. 队列进程是否正常运行
2. 任务执行成功率
3. 失败任务的重试情况
4. Redis连接状态

## 故障排除

### 1. 队列无法启动
- 检查Redis连接配置
- 确认Redis服务是否正常
- 检查PHP扩展是否安装

### 2. 任务执行失败
- 查看日志文件
- 检查数据库连接
- 验证订单数据完整性

### 3. 回退到同步处理
当队列系统出现问题时，系统会自动回退到原来的同步处理方式，确保功能正常。

## 性能优化建议

1. **Redis优化**: 确保Redis有足够的内存和合适的配置
2. **进程数量**: 根据服务器性能调整队列进程数量
3. **监控告警**: 设置队列积压和失败率告警
4. **定期清理**: 定期清理过期的队列数据

## 注意事项

1. 确保队列进程持续运行
2. 定期监控队列任务执行情况
3. 在高并发场景下注意Redis性能
4. 建议在测试环境充分验证后再部署到生产环境
