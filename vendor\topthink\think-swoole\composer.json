{"name": "topthink/think-swoole", "description": "Swoole extend for thinkphp", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">7.1", "ext-json": "*", "ext-swoole": ">=4.4.8", "nette/php-generator": "^3.2", "open-smf/connection-pool": "~1.0", "stechstudio/backoff": "^1.2", "swoole/ide-helper": "^4.3", "symfony/finder": "^4.3.2|^5.1", "topthink/framework": "^6.0"}, "require-dev": {"symfony/var-dumper": "^4.3|^5.1", "topthink/think-tracing": "^1.0", "topthink/think-queue": "^3.0"}, "autoload": {"psr-4": {"think\\swoole\\": "src"}, "files": ["src/helpers.php"]}, "extra": {"think": {"services": ["think\\swoole\\Service"], "config": {"swoole": "src/config/swoole.php"}}}, "config": {"preferred-install": "dist", "sort-packages": true, "platform-check": false, "platform": {"ext-swoole": "4.4.8", "ext-fileinfo": "1.0.4"}}}