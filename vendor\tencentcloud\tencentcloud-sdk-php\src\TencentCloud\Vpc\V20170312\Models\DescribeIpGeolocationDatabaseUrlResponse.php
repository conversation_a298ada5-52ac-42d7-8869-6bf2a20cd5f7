<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Vpc\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeIpGeolocationDatabaseUrl返回参数结构体
 *
 * @method string getDownLoadUrl() 获取IP地理位置库下载链接地址。
 * @method void setDownLoadUrl(string $DownLoadUrl) 设置IP地理位置库下载链接地址。
 * @method string getExpiredAt() 获取链接到期时间。按照`ISO8601`标准表示，并且使用`UTC`时间。
 * @method void setExpiredAt(string $ExpiredAt) 设置链接到期时间。按照`ISO8601`标准表示，并且使用`UTC`时间。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeIpGeolocationDatabaseUrlResponse extends AbstractModel
{
    /**
     * @var string IP地理位置库下载链接地址。
     */
    public $DownLoadUrl;

    /**
     * @var string 链接到期时间。按照`ISO8601`标准表示，并且使用`UTC`时间。
     */
    public $ExpiredAt;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param string $DownLoadUrl IP地理位置库下载链接地址。
     * @param string $ExpiredAt 链接到期时间。按照`ISO8601`标准表示，并且使用`UTC`时间。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("DownLoadUrl",$param) and $param["DownLoadUrl"] !== null) {
            $this->DownLoadUrl = $param["DownLoadUrl"];
        }

        if (array_key_exists("ExpiredAt",$param) and $param["ExpiredAt"] !== null) {
            $this->ExpiredAt = $param["ExpiredAt"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
