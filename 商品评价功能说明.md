# 商品评价功能优化说明

## 需求描述
实现商品评价的智能显示逻辑：
1. 优先显示当前商品的评价
2. 若当前商品没有评价，则显示店铺内所有商品的评价
3. 若店铺内商品无评价则不显示评价模块

## 实现方案

### 1. 修改的文件

#### 1.1 `app/api/logic/GoodsCommentLogic.php`
- **category()方法**: 增加对shop_id参数的支持，可以按店铺查询评价统计
- **lists()方法**: 增加智能查询逻辑，优先查询商品评价，无评价时自动切换到店铺评价

#### 1.2 `app/api/logic/GoodsLogic.php`
- **getGoodsDetail()方法**: 修改商品详情中的评价查询逻辑，实现智能评价显示

### 2. 核心逻辑

#### 2.1 商品详情评价逻辑
```php
// 1. 首先检查当前商品是否有评价
$goods_comment_count = Db::name('goods_comment')
    ->where(['goods_id' => $goodsId, 'del' => 0, 'status' => 1])
    ->count('id');

// 2. 如果商品没有评价，查询店铺评价
if($goods_comment_count == 0) {
    $shop_comment_count = Db::name('goods_comment')
        ->where(['shop_id' => $shop_id, 'del' => 0, 'status' => 1])
        ->count('id');
    
    if($shop_comment_count > 0) {
        // 使用店铺评价
        $comment_source = 'shop';
        $query_params = ['shop_id' => $shop_id];
    }
}
```

#### 2.2 评价列表查询逻辑
```php
// 1. 检查商品是否有评价
if(isset($get['goods_id']) && $get['goods_id']) {
    $goods_comment_count = GoodsComment::where(['goods_id' => $get['goods_id'], 'del' => 0, 'status' => 1])->count();
    
    if($goods_comment_count > 0) {
        // 查询商品评价
        $where[] = ['gc.goods_id', '=', $get['goods_id']];
    } else {
        // 查询店铺评价
        $shop_id = Goods::where('id', $get['goods_id'])->value('shop_id');
        $where[] = ['gc.shop_id', '=', $shop_id];
        $comment_source = 'shop';
    }
}
```

### 3. 新增字段

#### 3.1 商品详情返回数据
```json
{
    "comment": {
        "source": "goods|shop",  // 评价来源标识
        "goods_comment": 4.5,    // 平均评分
        "percent": {...},        // 评价分类统计
        "one": {...}            // 最新评价
    }
}
```

#### 3.2 评价列表返回数据
```json
{
    "lists": [...],
    "count": 10,
    "comment_source": "goods|shop",  // 评价来源标识
    "page_no": 1,
    "page_size": 10,
    "more": false
}
```

### 4. API接口

#### 4.1 商品详情接口
- **接口**: `GET /api/goods/getGoodsDetail`
- **参数**: `goods_id` (商品ID)
- **返回**: 包含智能评价数据的商品详情

#### 4.2 评价分类统计接口
- **接口**: `GET /api/goods_comment/category`
- **参数**: `goods_id` (商品ID) 或 `shop_id` (店铺ID)
- **返回**: 评价分类统计数据

#### 4.3 评价列表接口
- **接口**: `GET /api/goods_comment/lists`
- **参数**: `goods_id` (商品ID) 或 `shop_id` (店铺ID)
- **返回**: 评价列表数据，包含来源标识

### 5. 前端处理建议

#### 5.1 评价模块显示逻辑
```javascript
// 根据评价数量决定是否显示评价模块
if (goodsDetail.comment.goods_comment > 0 || goodsDetail.comment.percent.all_count > 0) {
    // 显示评价模块
    showCommentModule();
} else {
    // 隐藏评价模块
    hideCommentModule();
}
```

#### 5.2 评价来源提示
```javascript
// 根据评价来源显示不同的提示文字
if (goodsDetail.comment.source === 'shop') {
    showTip('当前商品暂无评价，以下为店铺其他商品评价');
} else {
    showTip('商品评价');
}
```

### 6. 兼容性说明

- **向后兼容**: 现有的API调用方式完全兼容，不需要修改前端代码
- **渐进增强**: 新增的`source`和`comment_source`字段为可选字段，前端可以选择性使用
- **性能优化**: 通过智能查询减少了无效的数据库查询

### 7. 测试验证

可以使用提供的测试文件 `test_comment_logic.php` 来验证功能：

```bash
php test_comment_logic.php
```

测试覆盖以下场景：
1. 商品有评价的情况
2. 商品没有评价但店铺有评价的情况
3. 商品和店铺都没有评价的情况

### 8. 注意事项

1. **数据一致性**: 确保商品和店铺的关联关系正确
2. **性能考虑**: 对于高并发场景，建议对评价统计数据进行缓存
3. **用户体验**: 前端应该明确提示用户当前显示的是商品评价还是店铺评价
4. **SEO优化**: 评价内容对商品页面的SEO有重要影响，建议保持评价内容的相关性
