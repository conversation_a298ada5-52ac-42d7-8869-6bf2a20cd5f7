<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Lighthouse\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 描述了启动配置创建实例的公网可访问性，声明了实例的公网使用计费模式，最大带宽等。
 *
 * @method string getInternetChargeType() 获取网络计费类型。
 * @method void setInternetChargeType(string $InternetChargeType) 设置网络计费类型。
 * @method integer getInternetMaxBandwidthOut() 获取公网出带宽上限，单位：Mbps。
 * @method void setInternetMaxBandwidthOut(integer $InternetMaxBandwidthOut) 设置公网出带宽上限，单位：Mbps。
 * @method boolean getPublicIpAssigned() 获取是否分配公网 IP。
 * @method void setPublicIpAssigned(boolean $PublicIpAssigned) 设置是否分配公网 IP。
 */
class InternetAccessible extends AbstractModel
{
    /**
     * @var string 网络计费类型。
     */
    public $InternetChargeType;

    /**
     * @var integer 公网出带宽上限，单位：Mbps。
     */
    public $InternetMaxBandwidthOut;

    /**
     * @var boolean 是否分配公网 IP。
     */
    public $PublicIpAssigned;

    /**
     * @param string $InternetChargeType 网络计费类型。
     * @param integer $InternetMaxBandwidthOut 公网出带宽上限，单位：Mbps。
     * @param boolean $PublicIpAssigned 是否分配公网 IP。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InternetChargeType",$param) and $param["InternetChargeType"] !== null) {
            $this->InternetChargeType = $param["InternetChargeType"];
        }

        if (array_key_exists("InternetMaxBandwidthOut",$param) and $param["InternetMaxBandwidthOut"] !== null) {
            $this->InternetMaxBandwidthOut = $param["InternetMaxBandwidthOut"];
        }

        if (array_key_exists("PublicIpAssigned",$param) and $param["PublicIpAssigned"] !== null) {
            $this->PublicIpAssigned = $param["PublicIpAssigned"];
        }
    }
}
