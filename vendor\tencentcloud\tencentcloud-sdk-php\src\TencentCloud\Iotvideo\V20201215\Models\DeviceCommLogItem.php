<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Iotvideo\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 设备通讯日志查询返回条目
 *
 * @method string getTime() 获取时间
 * @method void setTime(string $Time) 设置时间
 * @method string getType() 获取日志类型，device 设备上行，shadow 服务端下行。
 * @method void setType(string $Type) 设置日志类型，device 设备上行，shadow 服务端下行。
 * @method string getData() 获取通讯数据。
 * @method void setData(string $Data) 设置通讯数据。
 */
class DeviceCommLogItem extends AbstractModel
{
    /**
     * @var string 时间
     */
    public $Time;

    /**
     * @var string 日志类型，device 设备上行，shadow 服务端下行。
     */
    public $Type;

    /**
     * @var string 通讯数据。
     */
    public $Data;

    /**
     * @param string $Time 时间
     * @param string $Type 日志类型，device 设备上行，shadow 服务端下行。
     * @param string $Data 通讯数据。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Time",$param) and $param["Time"] !== null) {
            $this->Time = $param["Time"];
        }

        if (array_key_exists("Type",$param) and $param["Type"] !== null) {
            $this->Type = $param["Type"];
        }

        if (array_key_exists("Data",$param) and $param["Data"] !== null) {
            $this->Data = $param["Data"];
        }
    }
}
