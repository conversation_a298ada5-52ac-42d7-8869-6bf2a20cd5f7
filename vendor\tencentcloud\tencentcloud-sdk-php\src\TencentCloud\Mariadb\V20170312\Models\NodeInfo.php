<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Mariadb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 描述实例的各个DB节点信息
 *
 * @method string getNodeId() 获取DB节点ID
 * @method void setNodeId(string $NodeId) 设置DB节点ID
 * @method string getRole() 获取DB节点角色，取值为master或者slave
 * @method void setRole(string $Role) 设置DB节点角色，取值为master或者slave
 */
class NodeInfo extends AbstractModel
{
    /**
     * @var string DB节点ID
     */
    public $NodeId;

    /**
     * @var string DB节点角色，取值为master或者slave
     */
    public $Role;

    /**
     * @param string $NodeId DB节点ID
     * @param string $Role DB节点角色，取值为master或者slave
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("NodeId",$param) and $param["NodeId"] !== null) {
            $this->NodeId = $param["NodeId"];
        }

        if (array_key_exists("Role",$param) and $param["Role"] !== null) {
            $this->Role = $param["Role"];
        }
    }
}
