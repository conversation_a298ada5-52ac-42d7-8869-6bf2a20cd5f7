<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Live\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteLiveDomain请求参数结构体
 *
 * @method string getDomainName() 获取要删除的域名
 * @method void setDomainName(string $DomainName) 设置要删除的域名
 * @method integer getDomainType() 获取类型。0-推流，1-播放
 * @method void setDomainType(integer $DomainType) 设置类型。0-推流，1-播放
 */
class DeleteLiveDomainRequest extends AbstractModel
{
    /**
     * @var string 要删除的域名
     */
    public $DomainName;

    /**
     * @var integer 类型。0-推流，1-播放
     */
    public $DomainType;

    /**
     * @param string $DomainName 要删除的域名
     * @param integer $DomainType 类型。0-推流，1-播放
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("DomainName",$param) and $param["DomainName"] !== null) {
            $this->DomainName = $param["DomainName"];
        }

        if (array_key_exists("DomainType",$param) and $param["DomainType"] !== null) {
            $this->DomainType = $param["DomainType"];
        }
    }
}
