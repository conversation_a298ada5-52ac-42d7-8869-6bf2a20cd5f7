<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Taf\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CheckKol
 *
 * @method array getDataList() 获取BspData
 * @method void setDataList(array $DataList) 设置BspData
 */
class InputKolBspData extends AbstractModel
{
    /**
     * @var array BspData
     */
    public $DataList;

    /**
     * @param array $DataList BspData
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("DataList",$param) and $param["DataList"] !== null) {
            $this->DataList = [];
            foreach ($param["DataList"] as $key => $value){
                $obj = new InputKolDataList();
                $obj->deserialize($value);
                array_push($this->DataList, $obj);
            }
        }
    }
}
