<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Iai\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * GetCheckSimilarPersonJobIdList返回参数结构体
 *
 * @method array getJobIdInfos() 获取人员查重任务信息列表。
 * @method void setJobIdInfos(array $JobIdInfos) 设置人员查重任务信息列表。
 * @method integer getJobIdNum() 获取查重任务总数量。
 * @method void setJobIdNum(integer $JobIdNum) 设置查重任务总数量。
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class GetCheckSimilarPersonJobIdListResponse extends AbstractModel
{
    /**
     * @var array 人员查重任务信息列表。
     */
    public $JobIdInfos;

    /**
     * @var integer 查重任务总数量。
     */
    public $JobIdNum;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $JobIdInfos 人员查重任务信息列表。
     * @param integer $JobIdNum 查重任务总数量。
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("JobIdInfos",$param) and $param["JobIdInfos"] !== null) {
            $this->JobIdInfos = [];
            foreach ($param["JobIdInfos"] as $key => $value){
                $obj = new JobIdInfo();
                $obj->deserialize($value);
                array_push($this->JobIdInfos, $obj);
            }
        }

        if (array_key_exists("JobIdNum",$param) and $param["JobIdNum"] !== null) {
            $this->JobIdNum = $param["JobIdNum"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
