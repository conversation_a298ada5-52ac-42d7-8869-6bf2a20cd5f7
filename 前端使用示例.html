<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品评价功能示例</title>
    <style>
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .comment-section { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .comment-header { font-weight: bold; margin-bottom: 10px; }
        .comment-source-tip { background: #f0f8ff; padding: 8px; border-radius: 3px; margin-bottom: 10px; font-size: 14px; color: #666; }
        .comment-stats { display: flex; gap: 15px; margin-bottom: 15px; }
        .stat-item { text-align: center; }
        .stat-number { font-size: 18px; font-weight: bold; color: #ff6b35; }
        .stat-label { font-size: 12px; color: #666; }
        .comment-item { border-bottom: 1px solid #eee; padding: 10px 0; }
        .comment-item:last-child { border-bottom: none; }
        .user-info { display: flex; align-items: center; margin-bottom: 5px; }
        .avatar { width: 30px; height: 30px; border-radius: 50%; margin-right: 10px; }
        .username { font-weight: bold; }
        .rating { color: #ffa500; margin-left: 10px; }
        .comment-text { margin: 5px 0; }
        .comment-images { display: flex; gap: 5px; margin-top: 5px; }
        .comment-image { width: 60px; height: 60px; object-fit: cover; border-radius: 3px; }
        .no-comment { text-align: center; color: #999; padding: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>商品评价功能示例</h1>
        
        <!-- 评价模块 -->
        <div id="commentSection" class="comment-section" style="display: none;">
            <div class="comment-header">商品评价</div>
            
            <!-- 评价来源提示 -->
            <div id="commentSourceTip" class="comment-source-tip" style="display: none;"></div>
            
            <!-- 评价统计 -->
            <div id="commentStats" class="comment-stats">
                <div class="stat-item">
                    <div class="stat-number" id="allCount">0</div>
                    <div class="stat-label">全部</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="goodCount">0</div>
                    <div class="stat-label">好评</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="mediumCount">0</div>
                    <div class="stat-label">中评</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="badCount">0</div>
                    <div class="stat-label">差评</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="avgRating">0</div>
                    <div class="stat-label">平均评分</div>
                </div>
            </div>
            
            <!-- 最新评价 -->
            <div id="latestComment" style="display: none;">
                <h4>最新评价</h4>
                <div class="comment-item">
                    <div class="user-info">
                        <img id="userAvatar" class="avatar" src="" alt="用户头像">
                        <span id="username" class="username"></span>
                        <span id="userRating" class="rating"></span>
                    </div>
                    <div id="commentText" class="comment-text"></div>
                    <div id="commentImages" class="comment-images"></div>
                </div>
            </div>
            
            <!-- 评价列表 -->
            <div id="commentList">
                <h4>全部评价</h4>
                <div id="commentItems"></div>
            </div>
        </div>
        
        <!-- 无评价提示 -->
        <div id="noComment" class="no-comment" style="display: none;">
            <p>暂无评价</p>
        </div>
        
        <!-- 测试按钮 -->
        <div style="margin-top: 20px;">
            <button onclick="loadGoodsDetail(1)">测试商品1（有评价）</button>
            <button onclick="loadGoodsDetail(999)">测试商品999（无评价）</button>
        </div>
    </div>

    <script>
        // 模拟API调用
        async function loadGoodsDetail(goodsId) {
            try {
                // 模拟商品详情API调用
                const response = await fetch(`/api/goods/getGoodsDetail?goods_id=${goodsId}`);
                const result = await response.json();
                
                if (result.code === 1) {
                    renderCommentSection(result.data.comment);
                } else {
                    console.error('获取商品详情失败:', result.msg);
                }
            } catch (error) {
                console.error('API调用失败:', error);
                // 模拟数据用于演示
                if (goodsId === 1) {
                    renderCommentSection({
                        source: 'goods',
                        goods_comment: 4.5,
                        percent: {
                            all_count: 25,
                            good_count: 20,
                            medium_count: 3,
                            bad_count: 2
                        },
                        one: {
                            avatar: 'https://via.placeholder.com/30',
                            nickname: '用户123',
                            goods_comment: 5,
                            comment: '商品质量很好，物流也很快，推荐购买！',
                            image: ['https://via.placeholder.com/60', 'https://via.placeholder.com/60']
                        }
                    });
                } else {
                    renderCommentSection({
                        source: 'shop',
                        goods_comment: 4.2,
                        percent: {
                            all_count: 8,
                            good_count: 6,
                            medium_count: 1,
                            bad_count: 1
                        },
                        one: {
                            avatar: 'https://via.placeholder.com/30',
                            nickname: '用户456',
                            goods_comment: 4,
                            comment: '店铺服务不错，商品也还可以。',
                            image: []
                        }
                    });
                }
            }
        }
        
        // 渲染评价模块
        function renderCommentSection(commentData) {
            const commentSection = document.getElementById('commentSection');
            const noComment = document.getElementById('noComment');
            const commentSourceTip = document.getElementById('commentSourceTip');
            
            // 检查是否有评价数据
            if (!commentData || !commentData.percent || commentData.percent.all_count === 0) {
                // 没有评价，隐藏评价模块
                commentSection.style.display = 'none';
                noComment.style.display = 'block';
                return;
            }
            
            // 有评价，显示评价模块
            commentSection.style.display = 'block';
            noComment.style.display = 'none';
            
            // 显示评价来源提示
            if (commentData.source === 'shop') {
                commentSourceTip.style.display = 'block';
                commentSourceTip.textContent = '当前商品暂无评价，以下为店铺其他商品评价';
            } else {
                commentSourceTip.style.display = 'none';
            }
            
            // 渲染评价统计
            document.getElementById('allCount').textContent = commentData.percent.all_count || 0;
            document.getElementById('goodCount').textContent = commentData.percent.good_count || 0;
            document.getElementById('mediumCount').textContent = commentData.percent.medium_count || 0;
            document.getElementById('badCount').textContent = commentData.percent.bad_count || 0;
            document.getElementById('avgRating').textContent = commentData.goods_comment || 0;
            
            // 渲染最新评价
            const latestComment = document.getElementById('latestComment');
            if (commentData.one && Object.keys(commentData.one).length > 0) {
                latestComment.style.display = 'block';
                
                document.getElementById('userAvatar').src = commentData.one.avatar || 'https://via.placeholder.com/30';
                document.getElementById('username').textContent = commentData.one.nickname || '匿名用户';
                document.getElementById('userRating').textContent = '★'.repeat(commentData.one.goods_comment || 0);
                document.getElementById('commentText').textContent = commentData.one.comment || '';
                
                // 渲染评价图片
                const commentImages = document.getElementById('commentImages');
                commentImages.innerHTML = '';
                if (commentData.one.image && commentData.one.image.length > 0) {
                    commentData.one.image.forEach(imageUrl => {
                        const img = document.createElement('img');
                        img.src = imageUrl;
                        img.className = 'comment-image';
                        commentImages.appendChild(img);
                    });
                }
            } else {
                latestComment.style.display = 'none';
            }
        }
        
        // 页面加载时默认加载商品1的评价
        window.onload = function() {
            loadGoodsDetail(1);
        };
    </script>
</body>
</html>
