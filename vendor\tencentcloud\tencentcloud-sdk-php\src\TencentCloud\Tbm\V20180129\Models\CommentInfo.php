<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tbm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 用户评论内容类型
 *
 * @method string getComment() 获取用户评论内容
 * @method void setComment(string $Comment) 设置用户评论内容
 * @method string getDate() 获取评论的时间
 * @method void setDate(string $Date) 设置评论的时间
 */
class CommentInfo extends AbstractModel
{
    /**
     * @var string 用户评论内容
     */
    public $Comment;

    /**
     * @var string 评论的时间
     */
    public $Date;

    /**
     * @param string $Comment 用户评论内容
     * @param string $Date 评论的时间
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Comment",$param) and $param["Comment"] !== null) {
            $this->Comment = $param["Comment"];
        }

        if (array_key_exists("Date",$param) and $param["Date"] !== null) {
            $this->Date = $param["Date"];
        }
    }
}
