<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tcr\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 触发器触发条件
 *
 * @method string getInvokeMethod() 获取触发方式
 * @method void setInvokeMethod(string $InvokeMethod) 设置触发方式
 * @method string getInvokeExpr() 获取触发表达式
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setInvokeExpr(string $InvokeExpr) 设置触发表达式
注意：此字段可能返回 null，表示取不到有效值。
 */
class TriggerInvokeCondition extends AbstractModel
{
    /**
     * @var string 触发方式
     */
    public $InvokeMethod;

    /**
     * @var string 触发表达式
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $InvokeExpr;

    /**
     * @param string $InvokeMethod 触发方式
     * @param string $InvokeExpr 触发表达式
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("InvokeMethod",$param) and $param["InvokeMethod"] !== null) {
            $this->InvokeMethod = $param["InvokeMethod"];
        }

        if (array_key_exists("InvokeExpr",$param) and $param["InvokeExpr"] !== null) {
            $this->InvokeExpr = $param["InvokeExpr"];
        }
    }
}
