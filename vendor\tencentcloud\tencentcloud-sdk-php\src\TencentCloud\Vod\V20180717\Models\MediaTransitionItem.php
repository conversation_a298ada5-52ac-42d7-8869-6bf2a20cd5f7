<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Vod\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 转场信息
 *
 * @method float getDuration() 获取转场持续时间，单位为秒。进行转场处理的两个媒体片段，第二个片段在轨道上的起始时间会自动进行调整，设置为前面一个片段的结束时间减去转场的持续时间。
 * @method void setDuration(float $Duration) 设置转场持续时间，单位为秒。进行转场处理的两个媒体片段，第二个片段在轨道上的起始时间会自动进行调整，设置为前面一个片段的结束时间减去转场的持续时间。
 * @method array getTransitions() 获取转场操作列表。图像转场操作和音频转场操作各自最多支持一个。
 * @method void setTransitions(array $Transitions) 设置转场操作列表。图像转场操作和音频转场操作各自最多支持一个。
 */
class MediaTransitionItem extends AbstractModel
{
    /**
     * @var float 转场持续时间，单位为秒。进行转场处理的两个媒体片段，第二个片段在轨道上的起始时间会自动进行调整，设置为前面一个片段的结束时间减去转场的持续时间。
     */
    public $Duration;

    /**
     * @var array 转场操作列表。图像转场操作和音频转场操作各自最多支持一个。
     */
    public $Transitions;

    /**
     * @param float $Duration 转场持续时间，单位为秒。进行转场处理的两个媒体片段，第二个片段在轨道上的起始时间会自动进行调整，设置为前面一个片段的结束时间减去转场的持续时间。
     * @param array $Transitions 转场操作列表。图像转场操作和音频转场操作各自最多支持一个。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Duration",$param) and $param["Duration"] !== null) {
            $this->Duration = $param["Duration"];
        }

        if (array_key_exists("Transitions",$param) and $param["Transitions"] !== null) {
            $this->Transitions = [];
            foreach ($param["Transitions"] as $key => $value){
                $obj = new TransitionOpertion();
                $obj->deserialize($value);
                array_push($this->Transitions, $obj);
            }
        }
    }
}
