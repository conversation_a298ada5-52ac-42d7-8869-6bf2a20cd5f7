<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dc\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DescribeDirectConnectTunnelExtra返回参数结构体
 *
 * @method DirectConnectTunnelExtra getDirectConnectTunnelExtra() 获取专用通道扩展信息
 * @method void setDirectConnectTunnelExtra(DirectConnectTunnelExtra $DirectConnectTunnelExtra) 设置专用通道扩展信息
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DescribeDirectConnectTunnelExtraResponse extends AbstractModel
{
    /**
     * @var DirectConnectTunnelExtra 专用通道扩展信息
     */
    public $DirectConnectTunnelExtra;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param DirectConnectTunnelExtra $DirectConnectTunnelExtra 专用通道扩展信息
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("DirectConnectTunnelExtra",$param) and $param["DirectConnectTunnelExtra"] !== null) {
            $this->DirectConnectTunnelExtra = new DirectConnectTunnelExtra();
            $this->DirectConnectTunnelExtra->deserialize($param["DirectConnectTunnelExtra"]);
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
