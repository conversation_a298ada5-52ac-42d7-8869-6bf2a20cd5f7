<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Vod\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 转自适应码流信息
 *
 * @method array getAdaptiveDynamicStreamingSet() 获取转自适应码流信息数组。
 * @method void setAdaptiveDynamicStreamingSet(array $AdaptiveDynamicStreamingSet) 设置转自适应码流信息数组。
 */
class MediaAdaptiveDynamicStreamingInfo extends AbstractModel
{
    /**
     * @var array 转自适应码流信息数组。
     */
    public $AdaptiveDynamicStreamingSet;

    /**
     * @param array $AdaptiveDynamicStreamingSet 转自适应码流信息数组。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("AdaptiveDynamicStreamingSet",$param) and $param["AdaptiveDynamicStreamingSet"] !== null) {
            $this->AdaptiveDynamicStreamingSet = [];
            foreach ($param["AdaptiveDynamicStreamingSet"] as $key => $value){
                $obj = new AdaptiveDynamicStreamingInfoItem();
                $obj->deserialize($value);
                array_push($this->AdaptiveDynamicStreamingSet, $obj);
            }
        }
    }
}
