<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ecm\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ImportImage请求参数结构体
 *
 * @method string getImageId() 获取镜像的Id。
 * @method void setImageId(string $ImageId) 设置镜像的Id。
 * @method string getImageDescription() 获取镜像的描述。
 * @method void setImageDescription(string $ImageDescription) 设置镜像的描述。
 * @method string getSourceRegion() 获取源地域
 * @method void setSourceRegion(string $SourceRegion) 设置源地域
 */
class ImportImageRequest extends AbstractModel
{
    /**
     * @var string 镜像的Id。
     */
    public $ImageId;

    /**
     * @var string 镜像的描述。
     */
    public $ImageDescription;

    /**
     * @var string 源地域
     */
    public $SourceRegion;

    /**
     * @param string $ImageId 镜像的Id。
     * @param string $ImageDescription 镜像的描述。
     * @param string $SourceRegion 源地域
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ImageId",$param) and $param["ImageId"] !== null) {
            $this->ImageId = $param["ImageId"];
        }

        if (array_key_exists("ImageDescription",$param) and $param["ImageDescription"] !== null) {
            $this->ImageDescription = $param["ImageDescription"];
        }

        if (array_key_exists("SourceRegion",$param) and $param["SourceRegion"] !== null) {
            $this->SourceRegion = $param["SourceRegion"];
        }
    }
}
