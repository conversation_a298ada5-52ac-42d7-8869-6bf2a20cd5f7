<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Ds\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * DeleteAccount返回参数结构体
 *
 * @method array getDelSuccessList() 获取删除成功帐号ID列表
 * @method void setDelSuccessList(array $DelSuccessList) 设置删除成功帐号ID列表
 * @method array getDelFailedList() 获取删除失败帐号ID列表
 * @method void setDelFailedList(array $DelFailedList) 设置删除失败帐号ID列表
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class DeleteAccountResponse extends AbstractModel
{
    /**
     * @var array 删除成功帐号ID列表
     */
    public $DelSuccessList;

    /**
     * @var array 删除失败帐号ID列表
     */
    public $DelFailedList;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param array $DelSuccessList 删除成功帐号ID列表
     * @param array $DelFailedList 删除失败帐号ID列表
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("DelSuccessList",$param) and $param["DelSuccessList"] !== null) {
            $this->DelSuccessList = $param["DelSuccessList"];
        }

        if (array_key_exists("DelFailedList",$param) and $param["DelFailedList"] !== null) {
            $this->DelFailedList = $param["DelFailedList"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
