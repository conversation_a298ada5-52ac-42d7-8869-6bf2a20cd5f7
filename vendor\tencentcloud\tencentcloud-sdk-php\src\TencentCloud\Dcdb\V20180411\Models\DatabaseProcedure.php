<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Dcdb\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 数据库存储过程信息
 *
 * @method string getProc() 获取存储过程名称
 * @method void setProc(string $Proc) 设置存储过程名称
 */
class DatabaseProcedure extends AbstractModel
{
    /**
     * @var string 存储过程名称
     */
    public $Proc;

    /**
     * @param string $Proc 存储过程名称
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Proc",$param) and $param["Proc"] !== null) {
            $this->Proc = $param["Proc"];
        }
    }
}
