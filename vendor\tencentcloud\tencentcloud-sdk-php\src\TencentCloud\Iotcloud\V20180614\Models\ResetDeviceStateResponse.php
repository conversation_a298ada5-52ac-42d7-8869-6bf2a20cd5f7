<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Iotcloud\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * ResetDeviceState返回参数结构体
 *
 * @method integer getSuccessCount() 获取批量重置设备成功数
 * @method void setSuccessCount(integer $SuccessCount) 设置批量重置设备成功数
 * @method array getResetDeviceResults() 获取批量重置设备结果
 * @method void setResetDeviceResults(array $ResetDeviceResults) 设置批量重置设备结果
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class ResetDeviceStateResponse extends AbstractModel
{
    /**
     * @var integer 批量重置设备成功数
     */
    public $SuccessCount;

    /**
     * @var array 批量重置设备结果
     */
    public $ResetDeviceResults;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param integer $SuccessCount 批量重置设备成功数
     * @param array $ResetDeviceResults 批量重置设备结果
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("SuccessCount",$param) and $param["SuccessCount"] !== null) {
            $this->SuccessCount = $param["SuccessCount"];
        }

        if (array_key_exists("ResetDeviceResults",$param) and $param["ResetDeviceResults"] !== null) {
            $this->ResetDeviceResults = [];
            foreach ($param["ResetDeviceResults"] as $key => $value){
                $obj = new ResetDeviceResult();
                $obj->deserialize($value);
                array_push($this->ResetDeviceResults, $obj);
            }
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
